import { PrismaClient } from '@prisma/client'

// SECURITY: This file should ONLY be imported in server-side code (API routes, middleware, etc.)
// NEVER import this in client-side components or pages

// Ensure this is only used server-side
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Prisma client cannot be used on the client side!')
}

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
//
// Learn more:
// https://pris.ly/d/help/next-js-best-practices

const globalForPrisma = globalThis

export const prisma = globalForPrisma.prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.POSTGRE_DB,
    },
  },
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Helper function to safely disconnect Prisma (useful for serverless)
export async function disconnectPrisma() {
  await prisma.$disconnect()
}

// Helper function to check database connection
export async function checkDatabaseConnection() {
  try {
    await prisma.$queryRaw`SELECT 1`
    return { success: true, message: 'Database connection successful' }
  } catch (error) {
    console.error('Database connection failed:', error)
    return { success: false, message: 'Database connection failed', error: error.message }
  }
}

export default prisma
