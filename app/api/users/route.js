import { NextResponse } from "next/server";
import { prisma } from "@/libs/prisma";
import { cache } from "@/libs/redis";

// Get all users (for testing purposes)
export async function GET() {
  try {
    // Check cache first
    const cacheKey = 'all_users';
    let users = await cache.get(cacheKey);
    
    if (!users) {
      users = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
          hasAccess: true,
          customerId: true,
          priceId: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      });
      
      // Cache for 5 minutes
      await cache.set(cacheKey, users, 300);
    }

    return NextResponse.json({
      success: true,
      count: users.length,
      users: users
    });
  } catch (error) {
    console.error('Users API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users', message: error.message },
      { status: 500 }
    );
  }
}
