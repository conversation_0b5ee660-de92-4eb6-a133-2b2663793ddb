import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';
import dotenv from 'dotenv';

// Import configurations
import { testConnection } from './src/config/database.js';
import { testRedisConnection } from './src/config/redis.js';

// Import middleware
import { optionalAuth } from './src/middleware/auth.js';
import { addRealIPMiddleware } from './src/utils/ip.js';

// Import routes
import authRoutes from './src/routes/auth.js';
import apiRoutes from './src/routes/api.js';
import websocketRoutes from './src/routes/websocket.js';
import adminRoutes from './src/routes/admin.js';
import docsRoutes from './src/routes/docs.js';

// Import WebSocket server and stream manager
import { WSServer } from './src/websocket/WebSocketServer.js';
import { StreamManager } from './src/websocket/StreamManager.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;
const HOST = process.env.HOST || 'localhost';

// Create HTTP server
const server = createServer(app);

// Security middleware
app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false, // Disable CSP entirely
    hsts: process.env.NODE_ENV === 'production', // Only enable HSTS in production
}));

// CORS configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'production'
        ? ['https://stalkapi.com']
        : ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV !== 'test') {
    app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Trust proxy for accurate IP addresses (important for Cloudflare)
app.set('trust proxy', true);

// Add real IP middleware to extract real IP from Cloudflare headers
app.use(addRealIPMiddleware);

// Serve static files for documentation
app.use(express.static('public'));

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const dbHealthy = await testConnection();
        const redisHealthy = await testRedisConnection();

        const health = {
            status: dbHealthy && redisHealthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            services: {
                database: dbHealthy ? 'healthy' : 'unhealthy',
                redis: redisHealthy ? 'healthy' : 'unhealthy'
            },
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            version: '1.0.0'
        };

        res.status(dbHealthy && redisHealthy ? 200 : 503).json(health);
    } catch (error) {
        res.status(503).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// API routes
app.use('/auth', authRoutes);
app.use('/api/v1', apiRoutes);
app.use('/ws-api', websocketRoutes);
app.use('/admin', adminRoutes);
app.use('/docs', docsRoutes);

// Root endpoint
app.get('/', optionalAuth, (req, res) => {
    const welcomeMessage = {
        message: 'StalkApi',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
            authentication: '/auth',
            api: '/api/v1',
            websocket_info: '/ws-api',
            health: '/health',
            documentation: '/docs'
        },
        websocket: {
            endpoint: `wss://${req.get('host')}/ws`,
            authentication: 'API key required'
        }
    };

    res.json(welcomeMessage);
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Endpoint not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
    });
});

// Global error handler
app.use((error, req, res, next) => {
    console.error('Global error handler:', error);

    res.status(error.status || 500).json({
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
        ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
    });
});

// Initialize services
async function initializeServices() {
    try {
        console.log('🚀 Starting API Engine...');

        // Test database connection
        const dbConnected = await testConnection();
        if (!dbConnected) {
            throw new Error('Database connection failed');
        }

        // Test Redis connection
        const redisConnected = await testRedisConnection();
        if (!redisConnected) {
            throw new Error('Redis connection failed');
        }

        // Initialize WebSocket server
        const wsServer = new WSServer(server);

        // Initialize and start stream manager
        const streamManager = new StreamManager();
        await streamManager.start();

        // Store references for graceful shutdown
        app.locals.wsServer = wsServer;
        app.locals.streamManager = streamManager;

        console.log('✅ All services initialized successfully');

    } catch (error) {
        console.error('❌ Service initialization failed:', error);
        process.exit(1);
    }
}

// Start server
async function startServer() {
    try {
        await initializeServices();

        server.listen(PORT, HOST, () => {
            console.log(`🌟 API Engine running on http://${HOST}:${PORT}`);
            console.log(`🔌 WebSocket server available at ws://${HOST}:${PORT}/ws`);
            console.log(`📊 Health check: http://${HOST}:${PORT}/health`);
            console.log(`📚 API Documentation: http://${HOST}:${PORT}/docs`);
            console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

async function gracefulShutdown(signal) {
    console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

    try {
        // Stop accepting new connections
        server.close(() => {
            console.log('✅ HTTP server closed');
        });

        // Stop stream manager
        if (app.locals.streamManager) {
            await app.locals.streamManager.stop();
            console.log('✅ Stream manager stopped');
        }

        // Close database connections
        const { closePool } = await import('./src/config/database.js');
        await closePool();

        // Close Redis connections
        const { closeRedis } = await import('./src/config/redis.js');
        await closeRedis();

        console.log('✅ Graceful shutdown completed');
        process.exit(0);

    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// Start the application
if (process.env.NODE_ENV !== 'test') {
    startServer();
}

export default app;