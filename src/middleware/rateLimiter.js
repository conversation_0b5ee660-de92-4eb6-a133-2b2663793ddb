import rateLimit from 'express-rate-limit';
import { cache } from '../config/redis.js';

// Create a rate limiter based on user tier
export const createUserRateLimit = () => {
    return rateLimit({
        windowMs: parseInt(process.env.DEFAULT_RATE_WINDOW) * 60 * 1000 || 15 * 60 * 1000, // 15 minutes default
        keyGenerator: (req) => {
            // Use user ID if authenticated, otherwise real IP
            return req.user ? `user:${req.user.id}` : `ip:${req.realIP || req.ip}`;
        },
        limit: (req) => {
            // Return user's tier limit or default
            if (req.user && req.user.max_requests_per_minute) {
                return req.user.max_requests_per_minute;
            }
            return parseInt(process.env.DEFAULT_RATE_LIMIT) || 100;
        },
        message: (req) => ({
            error: 'Too many requests',
            limit: req.user ? req.user.max_requests_per_minute : parseInt(process.env.DEFAULT_RATE_LIMIT) || 100,
            windowMs: parseInt(process.env.DEFAULT_RATE_WINDOW) * 60 * 1000 || 15 * 60 * 1000,
            retryAfter: Math.ceil((parseInt(process.env.DEFAULT_RATE_WINDOW) * 60 * 1000) / 1000)
        }),
        standardHeaders: true,
        legacyHeaders: false,
        // Use Redis for distributed rate limiting
        store: {
            incr: async (key) => {
                const count = await cache.incr(key, Math.ceil((parseInt(process.env.DEFAULT_RATE_WINDOW) * 60 * 1000) / 1000));
                return { totalHits: count, resetTime: new Date(Date.now() + (parseInt(process.env.DEFAULT_RATE_WINDOW) * 60 * 1000)) };
            },
            decrement: async (key) => {
                // Redis doesn't need explicit decrement for our use case
            },
            resetKey: async (key) => {
                await cache.del(key);
            }
        }
    });
};

// Endpoint-specific rate limiting
export const createEndpointRateLimit = (maxRequests, windowMinutes = 15) => {
    return rateLimit({
        windowMs: windowMinutes * 60 * 1000,
        keyGenerator: (req) => {
            const endpoint = req.route?.path || req.path;
            return req.user ? `user:${req.user.id}:endpoint:${endpoint}` : `ip:${req.realIP || req.ip}:endpoint:${endpoint}`;
        },
        limit: maxRequests,
        message: {
            error: 'Too many requests to this endpoint',
            limit: maxRequests,
            windowMs: windowMinutes * 60 * 1000,
            retryAfter: windowMinutes * 60
        },
        standardHeaders: true,
        legacyHeaders: false,
        store: {
            incr: async (key) => {
                const count = await cache.incr(key, windowMinutes * 60);
                return { totalHits: count, resetTime: new Date(Date.now() + (windowMinutes * 60 * 1000)) };
            },
            decrement: async (key) => {
                // Redis doesn't need explicit decrement for our use case
            },
            resetKey: async (key) => {
                await cache.del(key);
            }
        }
    });
};

// WebSocket connection rate limiting
export const checkWebSocketRateLimit = async (userId, userTier) => {
    try {
        const key = `ws_connections:${userId}`;
        const currentConnections = await cache.get(key) || 0;
        const maxConnections = userTier.max_websocket_connections;
        
        if (currentConnections >= maxConnections) {
            return {
                allowed: false,
                current: currentConnections,
                max: maxConnections
            };
        }
        
        return {
            allowed: true,
            current: currentConnections,
            max: maxConnections
        };
    } catch (error) {
        console.error('WebSocket rate limit check error:', error);
        return { allowed: false, error: 'Rate limit check failed' };
    }
};

// Increment WebSocket connection count
export const incrementWebSocketConnections = async (userId) => {
    try {
        const key = `ws_connections:${userId}`;
        await cache.incr(key, 3600); // 1 hour TTL
        return true;
    } catch (error) {
        console.error('WebSocket connection increment error:', error);
        return false;
    }
};

// Decrement WebSocket connection count
export const decrementWebSocketConnections = async (userId) => {
    try {
        const key = `ws_connections:${userId}`;
        const current = await cache.get(key) || 0;
        
        if (current > 0) {
            await cache.set(key, current - 1, 3600);
        }
        
        return true;
    } catch (error) {
        console.error('WebSocket connection decrement error:', error);
        return false;
    }
};

// Custom rate limiter for specific use cases
export const customRateLimit = (options = {}) => {
    const {
        windowMs = 15 * 60 * 1000, // 15 minutes
        max = 100,
        keyGenerator = (req) => req.realIP || req.ip,
        message = 'Too many requests',
        skipSuccessfulRequests = false,
        skipFailedRequests = false
    } = options;
    
    return rateLimit({
        windowMs,
        limit: max,
        keyGenerator,
        message: typeof message === 'string' ? { error: message } : message,
        skipSuccessfulRequests,
        skipFailedRequests,
        standardHeaders: true,
        legacyHeaders: false,
        store: {
            incr: async (key) => {
                const count = await cache.incr(key, Math.ceil(windowMs / 1000));
                return { totalHits: count, resetTime: new Date(Date.now() + windowMs) };
            },
            decrement: async (key) => {
                // Redis doesn't need explicit decrement for our use case
            },
            resetKey: async (key) => {
                await cache.del(key);
            }
        }
    });
};
