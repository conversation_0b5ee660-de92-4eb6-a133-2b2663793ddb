const APIFeatures = () => {
  const features = [
    {
      icon: "⚡",
      title: "Lightning Fast",
      description: "Average response time under 50ms with global CDN distribution",
      stats: "50ms avg response"
    },
    {
      icon: "🔒",
      title: "Enterprise Security",
      description: "API keys, and rate limiting built-in",
      stats: "Bank-grade security"
    },
    {
      icon: "📊",
      title: "Real-time Analytics",
      description: "Monitor usage, track performance, and optimize your applications",
      stats: "Live monitoring"
    },
    {
      icon: "🚀",
      title: "Auto Scaling",
      description: "Handles traffic spikes automatically with zero downtime",
      stats: "99.9% uptime SLA"
    },
    {
      icon: "💳",
      title: "Credit-based Billing",
      description: "Pay only for what you use with transparent credit system",
      stats: "Fair usage pricing"
    },
    {
      icon: "🌐",
      title: "WebSocket Streaming",
      description: "Real-time data streaming with pub/sub architecture",
      stats: "Real-time data"
    }
  ];

  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Built for on-chain Alpha
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Everything you need to build, scale, and manage your API integrations.
            Automate your StalkChain Alpha insights.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="group bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-500"
            >
              {/* Icon */}
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>

              {/* Content */}
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
                {feature.description}
              </p>

              {/* Stats Badge */}
              <div className="inline-flex items-center px-3 py-1 bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium">
                {feature.stats}
              </div>
            </div>
          ))}
        </div>

        {/* API Endpoints Preview */}
        <div className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Comprehensive API Endpoints
            </h3>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Access powerful functionality through our well-documented REST API
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { endpoint: "/v1/demo", method: "GET", credits: 1, tier: "All Tiers", description: "Demo endpoint for testing" },
              { endpoint: "/v1/data", method: "GET", credits: 2, tier: "Basic+", description: "Data retrieval and processing" },
              { endpoint: "/v1/analytics", method: "GET", credits: 5, tier: "Premium+", description: "Advanced analytics and insights" },
              { endpoint: "/v1/batch", method: "POST", credits: 10, tier: "Enterprise", description: "Bulk data processing" }
            ].map((api, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 hover:border-purple-300 dark:hover:border-purple-500 transition-colors">
                <div className="flex items-center justify-between mb-3">
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${
                    api.method === 'GET'
                      ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300'
                      : 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                  }`}>
                    {api.method}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">{api.credits} credits</span>
                </div>

                <div className="font-mono text-sm text-gray-900 dark:text-white mb-2">
                  {api.endpoint}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                  {api.description}
                </p>

                <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                  {api.tier}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default APIFeatures;
