import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

export class User {
    constructor(data) {
        this.id = data.id;
        this.email = data.email;
        this.password_hash = data.password_hash;
        this.api_key = data.api_key;
        this.tier_id = data.tier_id;
        this.credits_remaining = data.credits_remaining;
        this.credits_used_this_month = data.credits_used_this_month;
        this.total_credits_purchased = data.total_credits_purchased;
        this.is_active = data.is_active;
        this.last_login = data.last_login;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;

        // Tier-related properties
        this.tier_name = data.tier_name;
        this.max_credits_per_month = data.max_credits_per_month;
        this.max_requests_per_minute = data.max_requests_per_minute;
        this.max_websocket_connections = data.max_websocket_connections;
        this.allowed_endpoints = data.allowed_endpoints;
        this.allowed_streams = data.allowed_streams;
        this.tier_is_enabled = data.is_enabled;
    }

    // Create a new user
    static async create(userData) {
        try {
            const { email, password, tier_id = 1 } = userData;
            
            // Check if user already exists
            const existingUser = await this.findByEmail(email);
            if (existingUser) {
                throw new Error('User already exists with this email');
            }
            
            // Hash password
            const password_hash = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS) || 12);
            
            // Generate API key
            const api_key = this.generateApiKey();
            
            // Get tier information to set initial credits (only enabled tiers)
            const tierResult = await query(
                'SELECT max_credits_per_month FROM access_tiers WHERE id = $1 AND is_enabled = true',
                [tier_id]
            );

            if (tierResult.rows.length === 0) {
                throw new Error('Invalid tier ID or tier is disabled');
            }
            
            const maxCredits = tierResult.rows[0].max_credits_per_month;
            const initialCredits = maxCredits === -1 ? 0 : maxCredits; // Unlimited tier gets 0, others get their monthly limit
            
            // Insert user
            const result = await query(
                `INSERT INTO users (email, password_hash, api_key, tier_id, credits_remaining)
                 VALUES ($1, $2, $3, $4, $5)
                 RETURNING *`,
                [email, password_hash, api_key, tier_id, initialCredits]
            );
            
            return new User(result.rows[0]);
            
        } catch (error) {
            console.error('Error creating user:', error);
            throw error;
        }
    }

    // Find user by ID
    static async findById(id) {
        try {
            // Check cache first
            const cacheKey = `user:${id}`;
            let userData = await cache.get(cacheKey);
            
            if (!userData) {
                const result = await query(
                    `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                            at.max_requests_per_minute, at.max_websocket_connections,
                            at.allowed_endpoints, at.allowed_streams, at.is_enabled
                     FROM users u
                     JOIN access_tiers at ON u.tier_id = at.id
                     WHERE u.id = $1 AND at.is_enabled = true`,
                    [id]
                );
                
                if (result.rows.length === 0) {
                    return null;
                }
                
                userData = result.rows[0];
                await cache.set(cacheKey, userData, 300); // Cache for 5 minutes
            }
            
            return new User(userData);
            
        } catch (error) {
            console.error('Error finding user by ID:', error);
            return null;
        }
    }

    // Find user by email
    static async findByEmail(email) {
        try {
            const result = await query(
                `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                        at.max_requests_per_minute, at.max_websocket_connections,
                        at.allowed_endpoints, at.allowed_streams, at.is_enabled
                 FROM users u
                 JOIN access_tiers at ON u.tier_id = at.id
                 WHERE u.email = $1 AND at.is_enabled = true`,
                [email]
            );
            
            if (result.rows.length === 0) {
                return null;
            }
            
            return new User(result.rows[0]);
            
        } catch (error) {
            console.error('Error finding user by email:', error);
            return null;
        }
    }

    // Find user by API key
    static async findByApiKey(apiKey) {
        try {
            // Check cache first
            const cacheKey = `apikey:${apiKey}`;
            let userData = await cache.get(cacheKey);
            
            if (!userData) {
                const result = await query(
                    `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                            at.max_requests_per_minute, at.max_websocket_connections,
                            at.allowed_endpoints, at.allowed_streams, at.is_enabled
                     FROM users u
                     JOIN access_tiers at ON u.tier_id = at.id
                     WHERE u.api_key = $1 AND u.is_active = true AND at.is_enabled = true`,
                    [apiKey]
                );
                
                if (result.rows.length === 0) {
                    return null;
                }
                
                userData = result.rows[0];
                await cache.set(cacheKey, userData, 300); // Cache for 5 minutes
            }
            
            return new User(userData);
            
        } catch (error) {
            console.error('Error finding user by API key:', error);
            return null;
        }
    }

    // Authenticate user with email and password
    static async authenticate(email, password) {
        try {
            const user = await this.findByEmail(email);
            if (!user) {
                return null;
            }
            
            const isValid = await bcrypt.compare(password, user.password_hash);
            if (!isValid) {
                return null;
            }
            
            // Update last login
            await query(
                'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
                [user.id]
            );
            
            // Invalidate cache
            await cache.del(`user:${user.id}`);
            
            return user;
            
        } catch (error) {
            console.error('Error authenticating user:', error);
            return null;
        }
    }

    // Note: JWT token generation removed - API now uses only API key authentication

    // Generate API key
    static generateApiKey() {
        return crypto.randomBytes(parseInt(process.env.API_KEY_LENGTH) || 32).toString('hex');
    }

    // Update user
    async update(updateData) {
        try {
            const allowedFields = ['email', 'tier_id', 'is_active'];
            const updates = [];
            const values = [];
            let paramCount = 1;
            
            for (const [key, value] of Object.entries(updateData)) {
                if (allowedFields.includes(key)) {
                    updates.push(`${key} = $${paramCount}`);
                    values.push(value);
                    paramCount++;
                }
            }
            
            if (updates.length === 0) {
                throw new Error('No valid fields to update');
            }
            
            values.push(this.id);
            
            const result = await query(
                `UPDATE users SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = $${paramCount} RETURNING *`,
                values
            );
            
            if (result.rows.length === 0) {
                throw new Error('User not found');
            }
            
            // Update instance properties
            Object.assign(this, result.rows[0]);
            
            // Invalidate cache
            await cache.del(`user:${this.id}`);
            await cache.del(`apikey:${this.api_key}`);
            
            return this;
            
        } catch (error) {
            console.error('Error updating user:', error);
            throw error;
        }
    }

    // Change password
    async changePassword(newPassword) {
        try {
            const password_hash = await bcrypt.hash(newPassword, parseInt(process.env.BCRYPT_ROUNDS) || 12);
            
            await query(
                'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
                [password_hash, this.id]
            );
            
            this.password_hash = password_hash;
            
            // Invalidate cache
            await cache.del(`user:${this.id}`);
            
            return true;
            
        } catch (error) {
            console.error('Error changing password:', error);
            return false;
        }
    }

    // Regenerate API key
    async regenerateApiKey() {
        try {
            const oldApiKey = this.api_key;
            const newApiKey = User.generateApiKey();
            
            await query(
                'UPDATE users SET api_key = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
                [newApiKey, this.id]
            );
            
            this.api_key = newApiKey;
            
            // Invalidate cache
            await cache.del(`user:${this.id}`);
            await cache.del(`apikey:${oldApiKey}`);
            
            return newApiKey;
            
        } catch (error) {
            console.error('Error regenerating API key:', error);
            return null;
        }
    }

    // Get user's usage statistics
    async getUsageStats(days = 30) {
        try {
            const result = await query(
                `SELECT 
                    COUNT(*) as total_requests,
                    SUM(credits_consumed) as total_credits_consumed,
                    AVG(response_time_ms) as avg_response_time,
                    COUNT(DISTINCT endpoint) as unique_endpoints,
                    DATE_TRUNC('day', created_at) as date,
                    COUNT(*) as daily_requests
                 FROM api_usage_logs 
                 WHERE user_id = $1 AND created_at >= CURRENT_DATE - INTERVAL '${days} days'
                 GROUP BY DATE_TRUNC('day', created_at)
                 ORDER BY date DESC`,
                [this.id]
            );
            
            return result.rows;
            
        } catch (error) {
            console.error('Error getting usage stats:', error);
            return [];
        }
    }

    // Serialize user data (remove sensitive information)
    toJSON() {
        const { password_hash, ...userData } = this;
        return userData;
    }
}
