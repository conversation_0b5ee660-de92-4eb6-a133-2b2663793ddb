import { Admin } from '../models/Admin.js';

/**
 * Admin authentication middleware using API key
 * Checks for admin API key in X-Admin-API-Key header
 */
export const requireAdmin = (requiredPermissions = []) => {
    return async (req, res, next) => {
        try {
            // Get admin API key from header
            const adminApiKey = req.headers['x-admin-api-key'];

            if (!adminApiKey) {
                return res.status(401).json({
                    error: 'Admin API key required',
                    code: 'ADMIN_API_KEY_REQUIRED',
                    message: 'Please provide a valid admin API key in the X-Admin-API-Key header'
                });
            }

            // Find admin by API key
            const admin = await Admin.findByApiKey(adminApiKey);

            if (!admin) {
                return res.status(401).json({
                    error: 'Invalid admin API key',
                    code: 'INVALID_ADMIN_API_KEY',
                    message: 'The provided admin API key is invalid or inactive'
                });
            }

            // Check if admin is active
            if (!admin.is_active) {
                return res.status(401).json({
                    error: 'Admin account inactive',
                    code: 'ADMIN_ACCOUNT_INACTIVE',
                    message: 'This admin account has been deactivated'
                });
            }

            // Check permissions if required
            if (requiredPermissions.length > 0) {
                const hasPermission = admin.hasAnyPermission(requiredPermissions);
                
                if (!hasPermission) {
                    return res.status(403).json({
                        error: 'Insufficient admin permissions',
                        code: 'INSUFFICIENT_ADMIN_PERMISSIONS',
                        message: `This operation requires one of the following permissions: ${requiredPermissions.join(', ')}`,
                        required_permissions: requiredPermissions,
                        admin_permissions: admin.permissions
                    });
                }
            }

            // Add admin to request object
            req.admin = admin;
            
            // Log admin access for security
            console.log(`Admin access: ${admin.email} (${admin.name}) - ${req.method} ${req.path}`);

            next();

        } catch (error) {
            console.error('Admin authentication error:', error);
            res.status(500).json({
                error: 'Admin authentication failed',
                code: 'ADMIN_AUTH_ERROR',
                message: 'An error occurred during admin authentication'
            });
        }
    };
};

/**
 * Middleware to require specific admin permission
 * @param {string} permission - Required permission (e.g., 'tiers:write')
 */
export const requirePermission = (permission) => {
    return requireAdmin([permission]);
};

/**
 * Middleware to require any of the specified admin permissions
 * @param {string[]} permissions - Array of acceptable permissions
 */
export const requireAnyPermission = (permissions) => {
    return requireAdmin(permissions);
};

/**
 * Middleware to require system admin access
 */
export const requireSystemAdmin = () => {
    return requireAdmin(['system:admin']);
};

/**
 * Optional admin authentication middleware
 * Adds admin to request if valid API key is provided, but doesn't require it
 */
export const optionalAdmin = async (req, res, next) => {
    try {
        const adminApiKey = req.headers['x-admin-api-key'];

        if (adminApiKey) {
            const admin = await Admin.findByApiKey(adminApiKey);
            
            if (admin && admin.is_active) {
                req.admin = admin;
                console.log(`Optional admin access: ${admin.email} (${admin.name}) - ${req.method} ${req.path}`);
            }
        }

        next();

    } catch (error) {
        console.error('Optional admin authentication error:', error);
        // Don't fail the request for optional admin auth
        next();
    }
};

/**
 * Middleware to log admin actions for audit trail
 */
export const logAdminAction = (action) => {
    return (req, res, next) => {
        if (req.admin) {
            // Log the admin action
            console.log(`Admin Action: ${req.admin.email} performed ${action} - ${req.method} ${req.path}`, {
                admin_id: req.admin.id,
                admin_email: req.admin.email,
                action: action,
                method: req.method,
                path: req.path,
                ip: req.realIP || req.ip,
                user_agent: req.headers['user-agent'],
                timestamp: new Date().toISOString()
            });

            // You could also store this in a database table for audit purposes
            // await query('INSERT INTO admin_audit_log (admin_id, action, details) VALUES ($1, $2, $3)', [...]);
        }

        next();
    };
};

/**
 * Get admin info for responses (removes sensitive data)
 * @param {Admin} admin - Admin instance
 * @returns {Object} - Safe admin info for responses
 */
export const getAdminInfo = (admin) => {
    return {
        id: admin.id,
        name: admin.name,
        email: admin.email,
        permissions: admin.permissions,
        is_active: admin.is_active,
        created_at: admin.created_at,
        last_used: admin.last_used
    };
};
