import { NextResponse } from "next/server";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys, checkRateLimit } from "@/libs/redis";

// This route is used to store the leads that are generated from the landing page.
// The API call is initiated by <ButtonLead /> component
// Duplicate emails just return 200 OK
export async function POST(req) {
  try {
    const body = await req.json();

    if (!body.email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 });
    }

    // Rate limiting - 5 requests per minute per IP
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    const rateLimitKey = CacheKeys.rateLimit(clientIP, 'lead');
    const rateLimit = await checkRateLimit(rateLimitKey, 5, 60);

    if (!rateLimit.allowed) {
      return NextResponse.json(
        { error: "Too many requests. Please try again later." },
        { status: 429 }
      );
    }

    const email = body.email.toLowerCase().trim();

    // Check cache first
    const cacheKey = CacheKeys.lead(email);
    const cachedLead = await cache.get(cacheKey);

    if (cachedLead) {
      // Lead already exists in cache, return success without database hit
      return NextResponse.json({ message: "Lead processed successfully" });
    }

    // Check if lead already exists in database
    const existingLead = await prisma.lead.findUnique({
      where: { email }
    });

    if (!existingLead) {
      // Create new lead
      const newLead = await prisma.lead.create({
        data: { email }
      });

      // Cache the lead for 24 hours
      await cache.set(cacheKey, { id: newLead.id, email: newLead.email }, 86400);

      console.log(`✅ New lead created: ${email}`);
    } else {
      // Cache existing lead for 24 hours
      await cache.set(cacheKey, { id: existingLead.id, email: existingLead.email }, 86400);
    }

    // Here you can add your own logic
    // For instance, sending a welcome email (use the sendEmail helper function from /libs/resend)
    // For instance, adding to email marketing list

    return NextResponse.json({ message: "Lead processed successfully" });
  } catch (e) {
    console.error("Lead API error:", e);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
