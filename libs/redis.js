import Redis from 'ioredis'

// SECURITY: This file should ONLY be imported in server-side code (API routes, middleware, etc.)
// NEVER import this in client-side components or pages

// Ensure this is only used server-side
if (typeof window !== 'undefined') {
  throw new Error('❌ SECURITY ERROR: Redis client cannot be used on the client side!')
}

// Redis configuration with security and performance optimizations
const redisConfig = {
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  
  // Connection pool settings
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxLoadingTimeout: 5000,
  
  // Security settings
  connectTimeout: 10000,
  commandTimeout: 5000,
  lazyConnect: true,
  
  // Performance settings
  keepAlive: 30000,
  family: 4, // IPv4
}

// Create Redis instance with error handling
let redis = null

function createRedisClient() {
  if (!redis) {
    redis = new Redis(redisConfig)
    
    redis.on('connect', () => {
      console.log('✅ Redis connected successfully')
    })
    
    redis.on('error', (err) => {
      console.error('❌ Redis connection error:', err.message)
    })
    
    redis.on('close', () => {
      console.log('🔌 Redis connection closed')
    })
    
    redis.on('reconnecting', () => {
      console.log('🔄 Redis reconnecting...')
    })
  }
  
  return redis
}

// Get Redis client instance
export function getRedisClient() {
  return createRedisClient()
}

// Cache helper functions with security and performance optimizations
export class CacheService {
  constructor() {
    this.redis = getRedisClient()
    this.defaultTTL = 3600 // 1 hour default TTL
  }
  
  // Set cache with optional TTL
  async set(key, value, ttl = this.defaultTTL) {
    try {
      const serializedValue = JSON.stringify(value)
      if (ttl) {
        await this.redis.setex(key, ttl, serializedValue)
      } else {
        await this.redis.set(key, serializedValue)
      }
      return true
    } catch (error) {
      console.error('Cache set error:', error)
      return false
    }
  }
  
  // Get cache
  async get(key) {
    try {
      const value = await this.redis.get(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }
  
  // Delete cache
  async del(key) {
    try {
      await this.redis.del(key)
      return true
    } catch (error) {
      console.error('Cache delete error:', error)
      return false
    }
  }
  
  // Delete multiple keys
  async delPattern(pattern) {
    try {
      const keys = await this.redis.keys(pattern)
      if (keys.length > 0) {
        await this.redis.del(...keys)
      }
      return true
    } catch (error) {
      console.error('Cache delete pattern error:', error)
      return false
    }
  }
  
  // Check if key exists
  async exists(key) {
    try {
      const result = await this.redis.exists(key)
      return result === 1
    } catch (error) {
      console.error('Cache exists error:', error)
      return false
    }
  }
  
  // Set TTL for existing key
  async expire(key, ttl) {
    try {
      await this.redis.expire(key, ttl)
      return true
    } catch (error) {
      console.error('Cache expire error:', error)
      return false
    }
  }
  
  // Get TTL for key
  async ttl(key) {
    try {
      return await this.redis.ttl(key)
    } catch (error) {
      console.error('Cache TTL error:', error)
      return -1
    }
  }
  
  // Increment counter
  async incr(key, ttl = this.defaultTTL) {
    try {
      const result = await this.redis.incr(key)
      if (result === 1 && ttl) {
        await this.redis.expire(key, ttl)
      }
      return result
    } catch (error) {
      console.error('Cache increment error:', error)
      return 0
    }
  }
  
  // Health check
  async ping() {
    try {
      const result = await this.redis.ping()
      return result === 'PONG'
    } catch (error) {
      console.error('Redis ping error:', error)
      return false
    }
  }
  
  // Graceful disconnect
  async disconnect() {
    try {
      await this.redis.quit()
      return true
    } catch (error) {
      console.error('Redis disconnect error:', error)
      return false
    }
  }
}

// Create singleton cache service instance
export const cache = new CacheService()

// Cache key generators for consistent naming
export const CacheKeys = {
  user: (id) => `user:${id}`,
  userByEmail: (email) => `user:email:${email}`,
  userAccess: (id) => `user:access:${id}`,
  lead: (email) => `lead:${email}`,
  stripeCustomer: (customerId) => `stripe:customer:${customerId}`,
  session: (token) => `session:${token}`,
  rateLimit: (ip, endpoint) => `rate_limit:${ip}:${endpoint}`,
}

// Rate limiting helper
export async function checkRateLimit(key, limit = 100, window = 3600) {
  try {
    const current = await cache.incr(key, window)
    return {
      allowed: current <= limit,
      remaining: Math.max(0, limit - current),
      resetTime: Date.now() + (window * 1000)
    }
  } catch (error) {
    console.error('Rate limit check error:', error)
    return { allowed: true, remaining: limit, resetTime: Date.now() + (window * 1000) }
  }
}

export default cache
