-- Credit Analytics and Optimization System
-- This migration adds comprehensive credit usage tracking and analysis

-- Create stream usage analytics table
CREATE TABLE IF NOT EXISTS stream_usage_analytics (
    id SERIAL PRIMARY KEY,
    stream_name VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    total_messages INTEGER DEFAULT 0,
    unique_subscribers INTEGER DEFAULT 0,
    total_credits_consumed INTEGER DEFAULT 0,
    avg_messages_per_user DECIMAL(10,2) DEFAULT 0,
    peak_messages_per_hour INTEGER DEFAULT 0,
    min_messages_per_hour INTEGER DEFAULT 0,
    total_connection_time_minutes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stream_name, date)
);

-- Create credit efficiency tracking table
CREATE TABLE IF NOT EXISTS credit_efficiency_log (
    id SERIAL PRIMARY KEY,
    stream_name VARCHAR(50) NOT NULL,
    tier_name VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    total_users INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    total_credits_allocated INTEGER DEFAULT 0,
    total_credits_used INTEGER DEFAULT 0,
    avg_credits_per_user DECIMAL(10,2) DEFAULT 0,
    utilization_rate DECIMAL(5,2) DEFAULT 0, -- percentage
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stream_name, tier_name, date)
);

-- Create tier migration tracking
CREATE TABLE IF NOT EXISTS tier_migration_log (
    id SERIAL PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    from_tier_id INTEGER REFERENCES access_tiers(id),
    to_tier_id INTEGER REFERENCES access_tiers(id),
    migration_reason VARCHAR(100),
    credits_remaining_before INTEGER DEFAULT 0,
    credits_remaining_after INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Function to update daily stream statistics
-- Only runs in production environment (NODE_ENV != 'development')
CREATE OR REPLACE FUNCTION update_daily_stream_stats(target_date DATE DEFAULT CURRENT_DATE)
RETURNS void AS $$
DECLARE
    stream_record RECORD;
    current_env TEXT;
BEGIN
    -- Check if we're in production environment
    SELECT current_setting('app.node_env', true) INTO current_env;

    -- Skip data collection in development environment
    IF current_env = 'development' THEN
        RAISE NOTICE 'Skipping analytics data collection in development environment';
        RETURN;
    END IF;
    -- Update stats for each active stream
    FOR stream_record IN 
        SELECT DISTINCT stream_name FROM stream_definitions WHERE is_active = true
    LOOP
        -- Calculate daily stats from WebSocket usage logs
        INSERT INTO stream_usage_analytics (
            stream_name, 
            date, 
            total_messages, 
            unique_subscribers, 
            total_credits_consumed,
            avg_messages_per_user
        )
        SELECT 
            stream_record.stream_name,
            target_date,
            COALESCE(COUNT(*), 0) as total_messages,
            COALESCE(COUNT(DISTINCT user_id), 0) as unique_subscribers,
            COALESCE(SUM(credits_consumed), 0) as total_credits_consumed,
            CASE 
                WHEN COUNT(DISTINCT user_id) > 0 
                THEN COUNT(*)::decimal / COUNT(DISTINCT user_id)
                ELSE 0 
            END as avg_messages_per_user
        FROM api_usage_logs 
        WHERE endpoint = 'websocket:/' || stream_record.stream_name
        AND DATE(created_at) = target_date
        ON CONFLICT (stream_name, date) 
        DO UPDATE SET
            total_messages = EXCLUDED.total_messages,
            unique_subscribers = EXCLUDED.unique_subscribers,
            total_credits_consumed = EXCLUDED.total_credits_consumed,
            avg_messages_per_user = EXCLUDED.avg_messages_per_user,
            updated_at = CURRENT_TIMESTAMP;
    END LOOP;
    
    RAISE NOTICE 'Updated daily stream stats for %', target_date;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate credit efficiency by tier
-- Only runs in production environment (NODE_ENV != 'development')
CREATE OR REPLACE FUNCTION update_credit_efficiency_stats(target_date DATE DEFAULT CURRENT_DATE)
RETURNS void AS $$
DECLARE
    tier_stream_record RECORD;
    current_env TEXT;
BEGIN
    -- Check if we're in production environment
    SELECT current_setting('app.node_env', true) INTO current_env;

    -- Skip data collection in development environment
    IF current_env = 'development' THEN
        RAISE NOTICE 'Skipping credit efficiency data collection in development environment';
        RETURN;
    END IF;
    -- Calculate efficiency for each tier/stream combination
    FOR tier_stream_record IN 
        SELECT 
            sd.stream_name,
            at.name as tier_name,
            at.id as tier_id,
            at.max_credits_per_month
        FROM stream_definitions sd
        CROSS JOIN access_tiers at
        WHERE sd.is_active = true 
        AND at.is_enabled = true
    LOOP
        INSERT INTO credit_efficiency_log (
            stream_name,
            tier_name,
            date,
            total_users,
            active_users,
            total_credits_allocated,
            total_credits_used,
            avg_credits_per_user,
            utilization_rate
        )
        SELECT 
            tier_stream_record.stream_name,
            tier_stream_record.tier_name,
            target_date,
            COUNT(DISTINCT u.id) as total_users,
            COUNT(DISTINCT aul.user_id) as active_users,
            COUNT(DISTINCT u.id) * GREATEST(tier_stream_record.max_credits_per_month, 0) as total_credits_allocated,
            COALESCE(SUM(aul.credits_consumed), 0) as total_credits_used,
            CASE 
                WHEN COUNT(DISTINCT aul.user_id) > 0 
                THEN SUM(aul.credits_consumed)::decimal / COUNT(DISTINCT aul.user_id)
                ELSE 0 
            END as avg_credits_per_user,
            CASE 
                WHEN tier_stream_record.max_credits_per_month > 0 
                THEN (COALESCE(SUM(aul.credits_consumed), 0)::decimal / 
                     (COUNT(DISTINCT u.id) * tier_stream_record.max_credits_per_month)) * 100
                ELSE 0 
            END as utilization_rate
        FROM users u
        LEFT JOIN api_usage_logs aul ON u.id = aul.user_id 
            AND aul.endpoint = 'websocket:/' || tier_stream_record.stream_name
            AND DATE(aul.created_at) = target_date
        WHERE u.tier_id = tier_stream_record.tier_id
        GROUP BY tier_stream_record.stream_name, tier_stream_record.tier_name
        ON CONFLICT (stream_name, tier_name, date)
        DO UPDATE SET
            total_users = EXCLUDED.total_users,
            active_users = EXCLUDED.active_users,
            total_credits_allocated = EXCLUDED.total_credits_allocated,
            total_credits_used = EXCLUDED.total_credits_used,
            avg_credits_per_user = EXCLUDED.avg_credits_per_user,
            utilization_rate = EXCLUDED.utilization_rate;
    END LOOP;
    
    RAISE NOTICE 'Updated credit efficiency stats for %', target_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get credit optimization recommendations
CREATE OR REPLACE FUNCTION get_credit_optimization_recommendations(days_back INTEGER DEFAULT 30)
RETURNS TABLE (
    stream_name VARCHAR(50),
    current_cost INTEGER,
    avg_daily_messages DECIMAL(10,2),
    avg_daily_users DECIMAL(10,2),
    messages_per_user DECIMAL(10,2),
    recommended_cost DECIMAL(10,4),
    basic_tier_capacity_days DECIMAL(10,2),
    premium_tier_capacity_days DECIMAL(10,2),
    optimization_notes TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sua.stream_name,
        sd.credits_per_message as current_cost,
        AVG(sua.total_messages) as avg_daily_messages,
        AVG(sua.unique_subscribers) as avg_daily_users,
        AVG(sua.avg_messages_per_user) as messages_per_user,
        -- Recommended cost: Basic tier should afford 80% of daily usage
        CASE 
            WHEN AVG(sua.total_messages) > 0 
            THEN GREATEST(0.01, (10000.0 * 0.8) / AVG(sua.total_messages))
            ELSE sd.credits_per_message::decimal
        END as recommended_cost,
        -- How many days of usage can Basic tier afford
        CASE 
            WHEN AVG(sua.total_messages) > 0 AND sd.credits_per_message > 0
            THEN 10000.0 / (AVG(sua.total_messages) * sd.credits_per_message)
            ELSE 999.99
        END as basic_tier_capacity_days,
        -- How many days of usage can Premium tier afford
        CASE 
            WHEN AVG(sua.total_messages) > 0 AND sd.credits_per_message > 0
            THEN 100000.0 / (AVG(sua.total_messages) * sd.credits_per_message)
            ELSE 999.99
        END as premium_tier_capacity_days,
        CASE 
            WHEN AVG(sua.total_messages) * sd.credits_per_message > 10000 
            THEN 'CRITICAL: Basic tier cannot afford 1 day of usage'
            WHEN AVG(sua.total_messages) * sd.credits_per_message > 3333 
            THEN 'WARNING: Basic tier can only afford ' || 
                 ROUND(10000.0 / (AVG(sua.total_messages) * sd.credits_per_message), 1) || ' days'
            WHEN sd.credits_per_message = 0 
            THEN 'INFO: Free stream - consider minimal cost for resource management'
            ELSE 'OK: Reasonable cost structure'
        END as optimization_notes
    FROM stream_usage_analytics sua
    JOIN stream_definitions sd ON sua.stream_name = sd.stream_name
    WHERE sua.date >= CURRENT_DATE - INTERVAL '1 day' * days_back
    GROUP BY sua.stream_name, sd.credits_per_message
    ORDER BY avg_daily_messages DESC;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_stream_usage_analytics_date_stream 
ON stream_usage_analytics(date, stream_name);

CREATE INDEX IF NOT EXISTS idx_credit_efficiency_log_date_tier_stream 
ON credit_efficiency_log(date, tier_name, stream_name);

CREATE INDEX IF NOT EXISTS idx_tier_migration_log_user_date 
ON tier_migration_log(user_id, created_at);

CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint_date 
ON api_usage_logs(endpoint, created_at) WHERE endpoint LIKE 'websocket:%';

-- Insert initial data for current streams
INSERT INTO stream_usage_analytics (stream_name, date, total_messages, unique_subscribers, total_credits_consumed)
VALUES 
    ('kol-feed', CURRENT_DATE, 0, 0, 0),
    ('jupiter-amm-swaps', CURRENT_DATE, 0, 0, 0),
    ('pumpfun-amm-swaps', CURRENT_DATE, 0, 0, 0),
    ('jupiter-dca-orders', CURRENT_DATE, 0, 0, 0)
ON CONFLICT (stream_name, date) DO NOTHING;

-- Create a view for easy credit analysis
CREATE OR REPLACE VIEW credit_analysis_summary AS
SELECT 
    sua.stream_name,
    sua.date,
    sua.total_messages,
    sua.unique_subscribers,
    sua.total_credits_consumed,
    sd.credits_per_message as current_cost,
    CASE 
        WHEN sua.total_messages > 0 
        THEN sua.total_credits_consumed::decimal / sua.total_messages 
        ELSE 0 
    END as actual_cost_per_message,
    CASE 
        WHEN sua.unique_subscribers > 0 
        THEN sua.total_credits_consumed::decimal / sua.unique_subscribers 
        ELSE 0 
    END as credits_per_user,
    -- Basic tier affordability (10,000 credits)
    CASE 
        WHEN sua.total_credits_consumed > 0 
        THEN 10000.0 / sua.total_credits_consumed 
        ELSE 999.99 
    END as basic_tier_days_affordable,
    -- Premium tier affordability (100,000 credits)
    CASE 
        WHEN sua.total_credits_consumed > 0 
        THEN 100000.0 / sua.total_credits_consumed 
        ELSE 999.99 
    END as premium_tier_days_affordable
FROM stream_usage_analytics sua
JOIN stream_definitions sd ON sua.stream_name = sd.stream_name
WHERE sua.date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY sua.date DESC, sua.stream_name;

COMMENT ON TABLE stream_usage_analytics IS 'Daily aggregated statistics for stream usage and credit consumption';
COMMENT ON TABLE credit_efficiency_log IS 'Credit utilization efficiency by tier and stream';
COMMENT ON TABLE tier_migration_log IS 'Track user tier changes and reasons';
COMMENT ON VIEW credit_analysis_summary IS 'Comprehensive view of credit costs and tier affordability';

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Credit analytics system installed successfully!';
    RAISE NOTICE 'Run SELECT * FROM get_credit_optimization_recommendations() to see current recommendations';
    RAISE NOTICE 'Run SELECT update_daily_stream_stats() to populate initial data';
END $$;
