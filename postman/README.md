# StalkAPI Postman Collection

This folder contains the comprehensive Postman collection for testing all aspects of the StalkAPI engine, including REST API endpoints and WebSocket connection information.

## 📦 Collection Overview

### 🔌 REST API Collection
**File**: `StalkAPI_Postman_Collection.json`

Complete collection for testing all REST API endpoints:
- **Authentication** - Login, registration, profile management
- **API v1 Endpoints** - Demo, data, analytics with credit tracking
- **WebSocket API Info** - Connection details and stream discovery
- **Admin API** - Tier management and credit administration (admin key required)

### ⚙️ Environment Configuration
**File**: `StalkAPI_Postman_Environment.json`

Environment variables for the collection:
- **Base URLs** - Development and production endpoints
- **Authentication** - JWT tokens and API keys
- **WebSocket URLs** - Real-time connection endpoints
- **Admin Keys** - Administrative access credentials

## 🚀 Quick Start

### 1. Import Collection
1. Open Postman
2. Click **Import** button
3. Select both JSON files:
   - `StalkAPI_Postman_Collection.json`
   - `StalkAPI_Postman_Environment.json`

### 2. Setup Environment
1. Select **"StalkAPI Environment"** from the environment dropdown
2. Update environment variables if needed:
   - `base_url` - API server URL (default: http://localhost:3001)
   - `websocket_url` - WebSocket server URL (default: ws://localhost:3001/ws)
   - `admin_api_key` - Admin API key for administrative functions

### 3. Authentication Setup
1. **Get JWT Token**:
   - Run "Login User" request in REST API collection
   - Token will be automatically saved to environment
2. **Get API Key**:
   - Available in user profile after login
   - Manually set in environment variables if needed

## 📋 Collection Details

### REST API Collection Structure

```
📁 Authentication
├── Register User
├── Login User
└── Refresh Token

📁 API v1 Endpoints
├── Demo Endpoint (1 credit)
├── Data Endpoint (2 credits)
└── Analytics Endpoint (5 credits)

📁 WebSocket API
├── Get WebSocket Info
├── Get Available Streams (JWT)
└── Get Available Streams (API Key)

📁 Admin API (Admin Key Required)
├── Get All Tiers
├── Enable/Disable Tiers
├── Update Tier Configuration
├── Get Tier Statistics
├── Get Admin Info
├── Get User Credits
├── Add Credits to User
├── Reset User Monthly Credits
└── Credit Analytics
```

### WebSocket Connection Information

The REST API collection includes WebSocket-related endpoints:
- **Get WebSocket Server Info** - Connection details and capabilities
- **Get Available Streams** - Stream discovery based on user tier
- **WebSocket URL** - Available in environment variables for manual testing

For actual WebSocket testing, use:
- **WebSocket Client** - Browser-based tools like WebSocket King or Postman's WebSocket feature
- **Node.js Test Script** - Run `node tests/websocket-test.js` for automated testing
- **Browser Console** - Use JavaScript WebSocket API for manual testing

## 🔧 Testing Workflows

### Basic API Testing
1. **Register/Login** → Get JWT token
2. **Test API Endpoints** → Verify credit consumption
3. **Check Profile** → Monitor credit balance
4. **WebSocket Info** → Get connection details

### WebSocket Testing
1. **Get WebSocket Info** → Check connection details and capabilities
2. **Get Available Streams** → Check tier permissions
3. **Use WebSocket Client** → Connect using provided URLs and authentication
4. **Run Test Script** → Use `tests/websocket-test.js` for automated testing

### Admin Testing
1. **Set Admin API Key** → Configure admin authentication
2. **Manage Tiers** → Enable/disable, update configuration
3. **User Management** → View credits, add credits, reset
4. **Analytics** → Monitor usage patterns

## 🎯 Stream Access by Tier

| Stream | Free | Basic | Premium | Enterprise |
|--------|------|-------|---------|------------|
| **demo-stream** | ✅ | ✅ | ✅ | ✅ |
| **data-stream** | ❌ | ✅ | ✅ | ✅ |
| **analytics-stream** | ❌ | ❌ | ✅ | ✅ |
| **enterprise-stream** | ❌ | ❌ | ❌ | ✅ |

## 💳 Credit Costs

| Endpoint | Credits | Notes |
|----------|---------|-------|
| `/api/v1/demo` | 1 | Basic functionality |
| `/api/v1/data` | 2 | Data retrieval |
| `/api/v1/analytics` | 5 | Complex processing |
| WebSocket connections | 0 | No credit cost |
| WebSocket messages | 0 | Real-time streaming |

## 🔐 Authentication Methods

### JWT Token Authentication
- **REST API**: `Authorization: Bearer <token>`
- **WebSocket**: `?token=<jwt_token>` query parameter
- **Expires**: Configurable (default: 24 hours)

### API Key Authentication
- **REST API**: `X-API-Key: <api_key>`
- **WebSocket**: `?apiKey=<api_key>` query parameter
- **Expires**: Never (permanent)

### Admin API Key Authentication
- **Admin Endpoints**: `X-Admin-API-Key: <admin_key>`
- **Permissions**: Granular permission system
- **Expires**: Never (permanent)

## 🧪 Testing Scenarios

### Credit System Testing
1. **Monitor Credits** → Check initial balance
2. **Make API Calls** → Consume credits
3. **Check Balance** → Verify deduction
4. **Test Limits** → Exceed monthly limit
5. **Admin Reset** → Restore credits

### Tier Access Testing
1. **Free Tier** → Test limited access
2. **Upgrade Tier** → Admin tier change
3. **Test New Access** → Verify expanded permissions
4. **WebSocket Limits** → Test connection limits

### Error Handling Testing
1. **Invalid Credentials** → Test authentication failures
2. **Insufficient Credits** → Test credit exhaustion
3. **Tier Restrictions** → Test unauthorized access
4. **Rate Limiting** → Test request limits

## 📊 Response Examples

### Successful API Response
```json
{
  "success": true,
  "data": { ... },
  "credits_remaining": 9949,
  "credits_used": 1
}
```

### WebSocket Stream Data
```json
{
  "stream": "demo-stream",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "value": 42,
    "status": "active"
  }
}
```

### Error Response
```json
{
  "error": "Insufficient credits",
  "code": "INSUFFICIENT_CREDITS",
  "credits_remaining": 0,
  "credits_required": 5
}
```

## 🔧 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `base_url` | API server URL | `http://localhost:3001` |
| `websocket_url` | WebSocket server URL | `ws://localhost:3001/ws` |
| `jwt_token` | User authentication token | Auto-set on login |
| `api_key` | User API key | From admin endpoints |
| `admin_api_key` | Admin authentication key | Set manually |
| `user_id` | Current user ID | Auto-set on login |

## 📚 Additional Resources

- **[API Documentation](../docs/API_DOCUMENTATION.md)** - Complete API reference
- **[WebSocket Guide](../docs/API_DOCUMENTATION.md#websocket-api)** - WebSocket implementation details
- **[Credit System](../docs/CREDIT_SYSTEM_GUIDE.md)** - Credit system documentation
- **[Postman Guide](../docs/POSTMAN_GUIDE.md)** - Detailed usage instructions

## 🆘 Troubleshooting

### Common Issues
1. **Connection Failed** → Check server is running on correct port
2. **Authentication Error** → Verify token/API key is valid
3. **WebSocket Connection Drops** → Check network stability
4. **Credit Errors** → Verify sufficient credits available

### Debug Tips
1. **Check Console** → Postman console shows detailed logs
2. **Verify Environment** → Ensure correct environment selected
3. **Test Authentication** → Start with login/profile endpoints
4. **Monitor Server Logs** → Check application logs for errors

---

**Last Updated**: June 2025  
**Collections Version**: 1.0.0  
**Postman Version**: 10.0+
