# MongoDB to PostgreSQL Migration Guide

## 🚀 Migration Complete!

Your application has been successfully migrated from MongoDB to PostgreSQL with Redis caching for optimal performance and security.

## 🔧 What Changed

### Database Layer
- **Removed**: MongoDB, Mongoose
- **Added**: PostgreSQL with Prisma ORM
- **Added**: Redis for caching and performance optimization

### Security Improvements
- ✅ Server-side only database access (Prisma & Redis clients throw errors if used client-side)
- ✅ SQL injection protection via Prisma's type-safe queries
- ✅ Rate limiting on API endpoints
- ✅ Input validation and sanitization
- ✅ Secure connection pooling

### Performance Improvements
- ✅ Redis caching for frequently accessed data
- ✅ Optimized database queries with Prisma
- ✅ Connection pooling for better resource management
- ✅ Cache invalidation strategies

## 📁 File Changes

### New Files
- `prisma/schema.prisma` - Database schema definition
- `libs/prisma.js` - Secure Prisma client (server-side only)
- `libs/redis.js` - Redis client with caching utilities (server-side only)
- `scripts/setup-database.js` - Database setup script

### Updated Files
- `package.json` - Updated dependencies and scripts
- `libs/next-auth.js` - Updated to use Prisma adapter
- `app/api/lead/route.js` - Migrated to Prisma with Redis caching
- `app/api/webhook/stripe/route.js` - Migrated to Prisma with Redis caching
- `app/api/stripe/create-checkout/route.js` - Migrated to Prisma with Redis caching
- `app/api/stripe/create-portal/route.js` - Migrated to Prisma with Redis caching

### Removed Files
- `libs/mongoose.js`
- `libs/mongo.js`
- `models/User.js`
- `models/Lead.js`
- `models/plugins/toJSON.js`

## 🛠 Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Database
```bash
npm run db:setup
```

This will:
- Generate Prisma client
- Push schema to PostgreSQL
- Test database connection
- Test Redis connection

### 3. Start Development Server
```bash
npm run dev
```

## 📊 Database Schema

### Users Table
- `id` - Unique identifier (CUID)
- `name` - User's name
- `email` - User's email (unique)
- `emailVerified` - Email verification timestamp
- `image` - Profile image URL
- `customerId` - Stripe customer ID
- `priceId` - Stripe price ID
- `hasAccess` - Access control boolean
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

### Leads Table
- `id` - Unique identifier (CUID)
- `email` - Lead email (unique)
- `createdAt` - Creation timestamp
- `updatedAt` - Last update timestamp

### NextAuth Tables
- `accounts` - OAuth account information
- `sessions` - User sessions
- `verificationtokens` - Email verification tokens

## 🔄 Caching Strategy

### User Data Caching
- User profiles cached for 1 hour
- User access status cached for 1 hour
- Cache invalidation on user updates

### Lead Data Caching
- Lead emails cached for 24 hours
- Prevents duplicate database hits

### Rate Limiting
- Lead API: 5 requests per minute per IP
- Cached rate limit counters

## 🔒 Security Features

### Server-Side Only Access
```javascript
// ❌ This will throw an error if used client-side
import { prisma } from '@/libs/prisma'
import { cache } from '@/libs/redis'
```

### Input Validation
- Email format validation
- SQL injection protection via Prisma
- Rate limiting on sensitive endpoints

### Environment Variables
```env
POSTGRE_DB="postgresql://..."
REDIS_HOST=**************
REDIS_PORT=6379
REDIS_PASSWORD=...
REDIS_DB=0
```

## 🚀 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run db:setup` - Setup database and test connections
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Create and run migrations
- `npm run db:studio` - Open Prisma Studio (database GUI)

## 🔍 Monitoring & Debugging

### Database Queries
In development, Prisma logs all queries to the console for debugging.

### Redis Connection
The setup script tests Redis connectivity and reports status.

### Error Handling
All database operations include proper error handling and logging.

## 🎯 Next Steps

1. **Test all functionality** - Verify user registration, payments, etc.
2. **Run tests** - Create and run tests for the new database layer
3. **Monitor performance** - Check Redis cache hit rates
4. **Backup strategy** - Set up PostgreSQL backups
5. **Production deployment** - Update production environment variables

## 📞 Support

If you encounter any issues:
1. Check the console for error messages
2. Verify environment variables are set correctly
3. Ensure PostgreSQL and Redis are running
4. Run `npm run db:setup` to test connections

## 🔄 Rollback Plan

If you need to rollback to MongoDB:
1. Restore the removed files from git history
2. Update package.json dependencies
3. Update environment variables
4. Restart the application

---

**Migration completed successfully! 🎉**
