#!/usr/bin/env node

/**
 * Credit Analysis Dashboard
 * Run this script to get immediate insights into your credit system
 * 
 * Usage: node scripts/credit-analysis-dashboard.js
 */

import { db } from '../src/config/database.js';
import chalk from 'chalk';

class CreditAnalysisDashboard {
    static async run() {
        console.log(chalk.blue.bold('\n🔍 StalkAPI Credit Analysis Dashboard\n'));
        
        try {
            await this.showCurrentUsage();
            await this.showCreditOptimization();
            await this.showTierAnalysis();
            await this.showRecommendations();
        } catch (error) {
            console.error(chalk.red('❌ Dashboard error:'), error.message);
        } finally {
            await db.end();
        }
    }
    
    static async showCurrentUsage() {
        console.log(chalk.yellow.bold('📊 Current Usage (Today)'));
        console.log('─'.repeat(80));
        
        try {
            const usage = await db.query(`
                SELECT 
                    'kol-feed' as stream,
                    COUNT(*) as messages,
                    COUNT(DISTINCT user_id) as users,
                    SUM(credits_consumed) as credits,
                    CASE WHEN COUNT(*) > 0 THEN SUM(credits_consumed)::decimal / COUNT(*) ELSE 0 END as cost_per_msg
                FROM api_usage_logs 
                WHERE endpoint = 'websocket:/kol-feed' 
                AND DATE(created_at) = CURRENT_DATE
                
                UNION ALL
                
                SELECT 
                    'jupiter-amm-swaps' as stream,
                    COUNT(*) as messages,
                    COUNT(DISTINCT user_id) as users,
                    SUM(credits_consumed) as credits,
                    CASE WHEN COUNT(*) > 0 THEN SUM(credits_consumed)::decimal / COUNT(*) ELSE 0 END as cost_per_msg
                FROM api_usage_logs 
                WHERE endpoint = 'websocket:/jupiter-amm-swaps' 
                AND DATE(created_at) = CURRENT_DATE
                
                ORDER BY messages DESC
            `);
            
            if (usage.rows.length === 0) {
                console.log(chalk.gray('No usage data for today yet.'));
                return;
            }
            
            usage.rows.forEach(row => {
                const status = row.messages > 0 ? chalk.green('ACTIVE') : chalk.gray('INACTIVE');
                console.log(`${status} ${chalk.cyan(row.stream.padEnd(20))} | ${chalk.white(row.messages.toString().padStart(8))} msgs | ${chalk.white(row.users.toString().padStart(4))} users | ${chalk.yellow(row.credits.toString().padStart(8))} credits`);
            });
            
        } catch (error) {
            console.log(chalk.red('❌ Error fetching current usage:', error.message));
        }
        
        console.log('');
    }
    
    static async showCreditOptimization() {
        console.log(chalk.yellow.bold('💡 Credit Optimization Analysis'));
        console.log('─'.repeat(80));
        
        try {
            // First, update stats for today
            await db.query('SELECT update_daily_stream_stats()');
            
            const recommendations = await db.query(`
                SELECT * FROM get_credit_optimization_recommendations(7)
            `);
            
            if (recommendations.rows.length === 0) {
                console.log(chalk.gray('No optimization data available. Run the system for a few days to collect data.'));
                return;
            }
            
            recommendations.rows.forEach(rec => {
                console.log(chalk.cyan.bold(`\n📈 ${rec.stream_name.toUpperCase()}`));
                console.log(`Current Cost: ${chalk.yellow(rec.current_cost)} credits/message`);
                console.log(`Recommended: ${chalk.green(parseFloat(rec.recommended_cost).toFixed(4))} credits/message`);
                console.log(`Daily Messages: ${chalk.white(Math.round(rec.avg_daily_messages))}`);
                console.log(`Daily Users: ${chalk.white(Math.round(rec.avg_daily_users))}`);
                console.log(`Basic Tier Capacity: ${chalk.white(parseFloat(rec.basic_tier_capacity_days).toFixed(1))} days`);
                console.log(`Premium Tier Capacity: ${chalk.white(parseFloat(rec.premium_tier_capacity_days).toFixed(1))} days`);
                
                if (rec.optimization_notes.includes('CRITICAL')) {
                    console.log(chalk.red.bold(`⚠️  ${rec.optimization_notes}`));
                } else if (rec.optimization_notes.includes('WARNING')) {
                    console.log(chalk.yellow.bold(`⚠️  ${rec.optimization_notes}`));
                } else {
                    console.log(chalk.green(`✅ ${rec.optimization_notes}`));
                }
            });
            
        } catch (error) {
            console.log(chalk.red('❌ Error in optimization analysis:', error.message));
        }
        
        console.log('');
    }
    
    static async showTierAnalysis() {
        console.log(chalk.yellow.bold('🎯 Tier Capacity Analysis'));
        console.log('─'.repeat(80));
        
        try {
            const tiers = await db.query(`
                SELECT 
                    name,
                    max_credits_per_month,
                    max_requests_per_minute,
                    max_websocket_connections,
                    array_length(allowed_streams, 1) as stream_count,
                    CASE 
                        WHEN allowed_streams = ARRAY['*'] THEN 'All Streams'
                        ELSE array_to_string(allowed_streams, ', ')
                    END as streams
                FROM access_tiers 
                WHERE is_enabled = true
                ORDER BY max_credits_per_month
            `);
            
            console.log(chalk.cyan('Tier'.padEnd(12)) + chalk.cyan('Credits/Month'.padEnd(15)) + chalk.cyan('Rate Limit'.padEnd(12)) + chalk.cyan('Connections'.padEnd(12)) + chalk.cyan('Streams'));
            console.log('─'.repeat(80));
            
            tiers.rows.forEach(tier => {
                const credits = tier.max_credits_per_month === -1 ? 'Unlimited' : tier.max_credits_per_month.toLocaleString();
                const rateLimit = `${tier.max_requests_per_minute}/min`;
                const connections = tier.max_websocket_connections.toString();
                
                console.log(
                    chalk.white(tier.name.padEnd(12)) +
                    chalk.yellow(credits.padEnd(15)) +
                    chalk.white(rateLimit.padEnd(12)) +
                    chalk.white(connections.padEnd(12)) +
                    chalk.gray(tier.streams)
                );
            });
            
        } catch (error) {
            console.log(chalk.red('❌ Error in tier analysis:', error.message));
        }
        
        console.log('');
    }
    
    static async showRecommendations() {
        console.log(chalk.yellow.bold('🚀 Recommendations'));
        console.log('─'.repeat(80));
        
        try {
            // Calculate KOL feed impact
            const kolStats = await db.query(`
                SELECT 
                    AVG(total_messages) as avg_daily_messages,
                    MAX(total_messages) as peak_daily_messages
                FROM stream_usage_analytics 
                WHERE stream_name = 'kol-feed' 
                AND date >= CURRENT_DATE - INTERVAL '7 days'
                AND total_messages > 0
            `);
            
            if (kolStats.rows[0] && kolStats.rows[0].avg_daily_messages > 0) {
                const avgDaily = parseFloat(kolStats.rows[0].avg_daily_messages);
                const peakDaily = parseFloat(kolStats.rows[0].peak_daily_messages);
                const currentCost = 2; // Current KOL feed cost
                
                const basicMonthlyCredits = 10000;
                const premiumMonthlyCredits = 100000;
                
                const basicDaysAffordable = basicMonthlyCredits / (avgDaily * currentCost);
                const premiumDaysAffordable = premiumMonthlyCredits / (avgDaily * currentCost);
                
                console.log(chalk.cyan.bold('📊 KOL Feed Analysis:'));
                console.log(`• Average daily messages: ${chalk.white(Math.round(avgDaily))}`);
                console.log(`• Peak daily messages: ${chalk.white(Math.round(peakDaily))}`);
                console.log(`• Current cost: ${chalk.yellow(currentCost)} credits/message`);
                console.log(`• Basic tier can afford: ${chalk.white(basicDaysAffordable.toFixed(1))} days/month`);
                console.log(`• Premium tier can afford: ${chalk.white(premiumDaysAffordable.toFixed(1))} days/month`);
                
                console.log(chalk.cyan.bold('\n💡 Immediate Actions:'));
                
                if (basicDaysAffordable < 1) {
                    console.log(chalk.red('🔥 CRITICAL: Reduce KOL feed cost to 0.3 credits/message for Basic tier viability'));
                } else if (basicDaysAffordable < 7) {
                    console.log(chalk.yellow('⚠️  WARNING: Consider reducing KOL feed cost to improve Basic tier value'));
                }
                
                if (premiumDaysAffordable < 30) {
                    console.log(chalk.yellow('⚠️  WARNING: Premium tier cannot afford full month of KOL feed'));
                }
                
                // Calculate optimal costs
                const optimalBasicCost = (basicMonthlyCredits * 0.8) / (avgDaily * 30); // 80% of credits for 30 days
                const optimalPremiumCost = (premiumMonthlyCredits * 0.6) / (avgDaily * 30); // 60% of credits for 30 days
                
                console.log(chalk.green.bold('\n🎯 Optimal Credit Costs:'));
                console.log(`• For Basic tier (80% usage): ${chalk.green(optimalBasicCost.toFixed(3))} credits/message`);
                console.log(`• For Premium tier (60% usage): ${chalk.green(optimalPremiumCost.toFixed(3))} credits/message`);
                console.log(`• Recommended: ${chalk.green.bold(Math.min(optimalBasicCost, optimalPremiumCost).toFixed(3))} credits/message`);
                
            } else {
                console.log(chalk.gray('No recent KOL feed data available for analysis.'));
            }
            
            console.log(chalk.cyan.bold('\n📋 Next Steps:'));
            console.log('1. Run this dashboard daily to monitor usage patterns');
            console.log('2. Implement the recommended credit costs');
            console.log('3. Monitor user tier migration patterns');
            console.log('4. Consider introducing usage-based pricing tiers');
            console.log('5. Set up automated alerts for unusual usage spikes');
            
        } catch (error) {
            console.log(chalk.red('❌ Error generating recommendations:', error.message));
        }
        
        console.log('\n' + '═'.repeat(80));
        console.log(chalk.blue.bold('📈 Dashboard Complete - Run again tomorrow for updated insights!'));
        console.log('═'.repeat(80) + '\n');
    }
}

// Run the dashboard
CreditAnalysisDashboard.run();
