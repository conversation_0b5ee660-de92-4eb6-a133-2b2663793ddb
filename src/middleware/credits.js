import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

// Middleware to consume credits after successful API call
export const consumeCredits = (creditsAmount = 1) => {
    return async (req, res, next) => {
        // Store original res.json to intercept response
        const originalJson = res.json;
        
        res.json = function(data) {
            // Only consume credits on successful responses (2xx status codes)
            if (res.statusCode >= 200 && res.statusCode < 300) {
                // Don't await this to avoid blocking the response
                consumeUserCredits(req, res, creditsAmount).catch(error => {
                    console.error('Credit consumption error:', error);
                });
            }
            
            // Call original json method
            return originalJson.call(this, data);
        };
        
        next();
    };
};

// Function to actually consume credits and log usage
const consumeUserCredits = async (req, res, creditsAmount) => {
    try {
        const user = req.user;
        
        // Skip credit consumption for unlimited tier
        if (user.max_credits_per_month === -1) {
            await logApiUsage(req, res, 0);
            return;
        }
        
        const endpoint = req.route?.path || req.path;
        const method = req.method;
        const responseTime = res.get('X-Response-Time') || null;
        const ipAddress = req.realIP || req.ip;
        const userAgent = req.get('User-Agent');
        
        // Prepare request payload (sanitize sensitive data)
        const requestPayload = sanitizePayload(req.body);
        
        // Use database function to consume credits and log usage
        const result = await query(
            'SELECT consume_credits($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)',
            [
                user.id,
                endpoint,
                method,
                creditsAmount,
                res.statusCode,
                responseTime ? parseInt(responseTime) : null,
                ipAddress,
                userAgent,
                requestPayload ? JSON.stringify(requestPayload) : null,
                null // response payload - we'll skip this for now to avoid storing large responses
            ]
        );
        
        const success = result.rows[0].consume_credits;
        
        if (!success) {
            console.error('Failed to consume credits for user:', user.id);
        } else {
            // Update cached user data
            const cacheKey = `user:${user.id}`;
            const cachedUser = await cache.get(cacheKey);
            if (cachedUser) {
                cachedUser.credits_remaining -= creditsAmount;
                cachedUser.credits_used_this_month += creditsAmount;
                await cache.set(cacheKey, cachedUser, 300);
            }
            
            // Also update API key cache if used
            if (req.authMethod === 'apikey') {
                const apiKeyCacheKey = `apikey:${user.api_key}`;
                const cachedApiKeyUser = await cache.get(apiKeyCacheKey);
                if (cachedApiKeyUser) {
                    cachedApiKeyUser.credits_remaining -= creditsAmount;
                    cachedApiKeyUser.credits_used_this_month += creditsAmount;
                    await cache.set(apiKeyCacheKey, cachedApiKeyUser, 300);
                }
            }
        }
        
    } catch (error) {
        console.error('Error consuming credits:', error);
    }
};

// Log API usage without consuming credits
const logApiUsage = async (req, res, creditsAmount = 0) => {
    try {
        const user = req.user;
        const endpoint = req.route?.path || req.path;
        const method = req.method;
        const responseTime = res.get('X-Response-Time') || null;
        const ipAddress = req.realIP || req.ip;
        const userAgent = req.get('User-Agent');
        const requestPayload = sanitizePayload(req.body);
        
        await query(
            `INSERT INTO api_usage_logs (
                user_id, endpoint, method, credits_consumed, response_status,
                response_time_ms, ip_address, user_agent, request_payload
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
            [
                user.id,
                endpoint,
                method,
                creditsAmount,
                res.statusCode,
                responseTime ? parseInt(responseTime) : null,
                ipAddress,
                userAgent,
                requestPayload ? JSON.stringify(requestPayload) : null
            ]
        );
        
    } catch (error) {
        console.error('Error logging API usage:', error);
    }
};

// Sanitize request payload to remove sensitive information
const sanitizePayload = (payload) => {
    if (!payload || typeof payload !== 'object') {
        return payload;
    }
    
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'authorization'];
    const sanitized = { ...payload };
    
    const sanitizeObject = (obj) => {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                const lowerKey = key.toLowerCase();
                if (sensitiveFields.some(field => lowerKey.includes(field))) {
                    obj[key] = '[REDACTED]';
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    sanitizeObject(obj[key]);
                }
            }
        }
    };
    
    sanitizeObject(sanitized);
    return sanitized;
};

// Middleware to add response time header
export const responseTime = (req, res, next) => {
    const start = Date.now();

    // Store original res.end to intercept response
    const originalEnd = res.end;

    res.end = function(...args) {
        const duration = Date.now() - start;
        res.set('X-Response-Time', duration.toString());
        return originalEnd.apply(this, args);
    };

    next();
};

// Get user's current credit status
export const getCreditStatus = async (userId) => {
    try {
        const result = await query(
            `SELECT u.credits_remaining, u.credits_used_this_month, u.total_credits_purchased,
                    at.max_credits_per_month, at.name as tier_name
             FROM users u
             JOIN access_tiers at ON u.tier_id = at.id
             WHERE u.id = $1`,
            [userId]
        );
        
        if (result.rows.length === 0) {
            return null;
        }
        
        const user = result.rows[0];
        return {
            credits_remaining: user.credits_remaining,
            credits_used_this_month: user.credits_used_this_month,
            total_credits_purchased: user.total_credits_purchased,
            max_credits_per_month: user.max_credits_per_month,
            tier_name: user.tier_name,
            unlimited: user.max_credits_per_month === -1
        };
        
    } catch (error) {
        console.error('Error getting credit status:', error);
        return null;
    }
};

// Add credits to user account
export const addCreditsToUser = async (userId, creditsAmount, transactionType = 'purchase', description = null, referenceId = null) => {
    try {
        const result = await query(
            'SELECT add_credits($1, $2, $3, $4, $5)',
            [userId, creditsAmount, transactionType, description, referenceId]
        );
        
        const success = result.rows[0].add_credits;
        
        if (success) {
            // Invalidate user cache
            await cache.del(`user:${userId}`);
            
            // Also invalidate API key cache if exists
            const userResult = await query('SELECT api_key FROM users WHERE id = $1', [userId]);
            if (userResult.rows.length > 0) {
                await cache.del(`apikey:${userResult.rows[0].api_key}`);
            }
        }
        
        return success;
        
    } catch (error) {
        console.error('Error adding credits:', error);
        return false;
    }
};
