"use client";

import { useState } from "react";
import Link from "next/link";
import ButtonAccount from "@/components/ButtonAccount";
import ThemeToggle from "@/components/ThemeToggle";

const DashboardHeader = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Navigation */}
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-2">
              <span className="font-extrabold text-2xl">
                Stalk<span className="text-primary">Api</span>
              </span>
            </Link>
            
            {/* Navigation Links */}
            <nav className="hidden md:flex space-x-6">
              <Link 
                href="/dashboard" 
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-medium"
              >
                Dashboard
              </Link>
              <Link 
                href="/dashboard/usage" 
                className="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors"
              >
                Usage
              </Link>
              <Link 
                href="/dashboard/settings" 
                className="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors"
              >
                Settings
              </Link>
              <Link 
                href="https://data.stalkapi.com/docs" 
                target="_blank"
                className="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors"
              >
                Docs
              </Link>
            </nav>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* API Status Indicator */}
            <div className="hidden sm:flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-full">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
              <span className="text-green-700 dark:text-green-300 text-sm font-medium">API Online</span>
            </div>

            <ThemeToggle />
            <ButtonAccount />

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-primary"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 dark:border-gray-700 py-4">
            <nav className="flex flex-col space-y-3">
              <Link 
                href="/dashboard" 
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-medium px-2 py-1"
                onClick={() => setIsMenuOpen(false)}
              >
                Dashboard
              </Link>
              <Link 
                href="/dashboard/usage" 
                className="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors px-2 py-1"
                onClick={() => setIsMenuOpen(false)}
              >
                Usage
              </Link>
              <Link 
                href="/dashboard/settings" 
                className="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors px-2 py-1"
                onClick={() => setIsMenuOpen(false)}
              >
                Settings
              </Link>
              <Link 
                href="https://data.stalkapi.com/docs" 
                target="_blank"
                className="text-gray-600 dark:text-gray-300 hover:text-primary transition-colors px-2 py-1"
                onClick={() => setIsMenuOpen(false)}
              >
                Documentation
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default DashboardHeader;
