import { NextResponse } from "next/server";
import { headers } from "next/headers";
import <PERSON><PERSON> from "stripe";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys } from "@/libs/redis";
import configFile from "@/config";
import { findCheckoutSession } from "@/libs/stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// This is where we receive Stripe webhook events
// It used to update the user data, send emails, etc...
// By default, it'll store the user in the database
// See more: https://shipfa.st/docs/features/payments
export async function POST(req) {

  const body = await req.text();

  const signature = headers().get("stripe-signature");

  let data;
  let eventType;
  let event;

  // verify Stripe event is legit
  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error(`Webhook signature verification failed. ${err.message}`);
    return NextResponse.json({ error: err.message }, { status: 400 });
  }

  data = event.data;
  eventType = event.type;

  try {
    switch (eventType) {
      case "checkout.session.completed": {
        // First payment is successful and a subscription is created (if mode was set to "subscription" in ButtonCheckout)
        // ✅ Grant access to the product

        const session = await findCheckoutSession(data.object.id);

        const customerId = session?.customer;
        const priceId = session?.line_items?.data[0]?.price.id;
        const userId = data.object.client_reference_id;
        const plan = configFile.stripe.plans.find((p) => p.priceId === priceId);

        if (!plan) break;

        const customer = await stripe.customers.retrieve(customerId);

        let user;

        // Get or create the user. userId is normally pass in the checkout session (clientReferenceID) to identify the user when we get the webhook event
        if (userId) {
          // Check cache first
          const userCacheKey = CacheKeys.user(userId);
          user = await cache.get(userCacheKey);

          if (!user) {
            user = await prisma.user.findUnique({
              where: { id: userId }
            });

            if (user) {
              // Cache user for 1 hour
              await cache.set(userCacheKey, user, 3600);
            }
          }
        } else if (customer.email) {
          const email = customer.email.toLowerCase().trim();

          // Check cache first
          const emailCacheKey = CacheKeys.userByEmail(email);
          user = await cache.get(emailCacheKey);

          if (!user) {
            user = await prisma.user.findUnique({
              where: { email }
            });

            if (!user) {
              user = await prisma.user.create({
                data: {
                  email,
                  name: customer.name,
                }
              });
            }

            // Cache user for 1 hour
            await cache.set(emailCacheKey, user, 3600);
            await cache.set(CacheKeys.user(user.id), user, 3600);
          }
        } else {
          console.error("No user found");
          throw new Error("No user found");
        }

        // Update user data + Grant user access to your product
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: {
            priceId,
            customerId,
            hasAccess: true,
          }
        });

        // Update cache with new user data
        await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
        await cache.set(CacheKeys.userByEmail(updatedUser.email), updatedUser, 3600);
        await cache.set(CacheKeys.userAccess(user.id), true, 3600);

        // Extra: send email with user link, product page, etc...
        // try {
        //   await sendEmail({to: ...});
        // } catch (e) {
        //   console.error("Email issue:" + e?.message);
        // }

        break;
      }

      case "checkout.session.expired": {
        // User didn't complete the transaction
        // You don't need to do anything here, by you can send an email to the user to remind him to complete the transaction, for instance
        break;
      }

      case "customer.subscription.updated": {
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        // You don't need to do anything here, because Stripe will let us know when the subscription is canceled for good (at the end of the billing cycle) in the "customer.subscription.deleted" event
        // You can update the user data to show a "Cancel soon" badge for instance
        break;
      }

      case "customer.subscription.deleted": {
        // The customer subscription stopped
        // ❌ Revoke access to the product
        // The customer might have changed the plan (higher or lower plan, cancel soon etc...)
        const subscription = await stripe.subscriptions.retrieve(
          data.object.id
        );

        // Find user by customerId with caching
        const customerCacheKey = CacheKeys.stripeCustomer(subscription.customer);
        let user = await cache.get(customerCacheKey);

        if (!user) {
          user = await prisma.user.findFirst({
            where: { customerId: subscription.customer }
          });

          if (user) {
            await cache.set(customerCacheKey, user, 3600);
          }
        }

        if (user) {
          // Revoke access to your product
          const updatedUser = await prisma.user.update({
            where: { id: user.id },
            data: { hasAccess: false }
          });

          // Update cache
          await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
          await cache.set(CacheKeys.userAccess(user.id), false, 3600);
          await cache.del(customerCacheKey); // Remove from cache to force refresh
        }

        break;
      }

      case "invoice.paid": {
        // Customer just paid an invoice (for instance, a recurring payment for a subscription)
        // ✅ Grant access to the product
        const priceId = data.object.lines.data[0].price.id;
        const customerId = data.object.customer;

        // Find user by customerId with caching
        const customerCacheKey = CacheKeys.stripeCustomer(customerId);
        let user = await cache.get(customerCacheKey);

        if (!user) {
          user = await prisma.user.findFirst({
            where: { customerId }
          });

          if (user) {
            await cache.set(customerCacheKey, user, 3600);
          }
        }

        if (!user) break;

        // Make sure the invoice is for the same plan (priceId) the user subscribed to
        if (user.priceId !== priceId) break;

        // Grant user access to your product
        const updatedUser = await prisma.user.update({
          where: { id: user.id },
          data: { hasAccess: true }
        });

        // Update cache
        await cache.set(CacheKeys.user(user.id), updatedUser, 3600);
        await cache.set(CacheKeys.userAccess(user.id), true, 3600);

        break;
      }

      case "invoice.payment_failed":
        // A payment failed (for instance the customer does not have a valid payment method)
        // ❌ Revoke access to the product
        // ⏳ OR wait for the customer to pay (more friendly):
        //      - Stripe will automatically email the customer (Smart Retries)
        //      - We will receive a "customer.subscription.deleted" when all retries were made and the subscription has expired

        break;

      default:
      // Unhandled event type
    }
  } catch (e) {
    console.error("stripe error: " + e.message + " | EVENT TYPE: " + eventType);
  }

  return NextResponse.json({});
}
