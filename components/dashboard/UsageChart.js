"use client";

import { useState } from "react";

const UsageChart = () => {
  const [timeRange, setTimeRange] = useState("7d");
  
  // Mock data for the chart - in real implementation, this would come from your API
  const chartData = {
    "24h": [
      { time: "00:00", calls: 45, credits: 90 },
      { time: "04:00", calls: 23, credits: 46 },
      { time: "08:00", calls: 67, credits: 134 },
      { time: "12:00", calls: 89, credits: 178 },
      { time: "16:00", calls: 156, credits: 312 },
      { time: "20:00", calls: 134, credits: 268 },
    ],
    "7d": [
      { time: "Mon", calls: 234, credits: 468 },
      { time: "Tue", calls: 345, credits: 690 },
      { time: "Wed", calls: 456, credits: 912 },
      { time: "Thu", calls: 567, credits: 1134 },
      { time: "Fri", calls: 678, credits: 1356 },
      { time: "Sat", calls: 432, credits: 864 },
      { time: "Sun", calls: 321, credits: 642 },
    ],
    "30d": [
      { time: "Week 1", calls: 2340, credits: 4680 },
      { time: "Week 2", calls: 3450, credits: 6900 },
      { time: "Week 3", calls: 4560, credits: 9120 },
      { time: "Week 4", calls: 5670, credits: 11340 },
    ]
  };

  const currentData = chartData[timeRange];
  const maxValue = Math.max(...currentData.map(d => Math.max(d.calls, d.credits)));

  return (
    <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            API Usage Overview
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Track your API calls and credit consumption
          </p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {["24h", "7d", "30d"].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                timeRange === range
                  ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                  : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Chart Area */}
      <div className="relative h-64">
        <div className="absolute inset-0 flex items-end justify-between space-x-2">
          {currentData.map((data, index) => (
            <div key={index} className="flex-1 flex flex-col items-center space-y-2">
              {/* Bars */}
              <div className="w-full flex space-x-1 items-end h-48">
                {/* API Calls Bar */}
                <div className="flex-1 bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-sm relative group">
                  <div 
                    className="w-full bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-sm transition-all duration-300"
                    style={{ height: `${(data.calls / maxValue) * 100}%` }}
                  ></div>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    {data.calls} calls
                  </div>
                </div>
                
                {/* Credits Bar */}
                <div className="flex-1 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm relative group">
                  <div 
                    className="w-full bg-gradient-to-t from-blue-500 to-blue-400 rounded-t-sm transition-all duration-300"
                    style={{ height: `${(data.credits / maxValue) * 100}%` }}
                  ></div>
                  
                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    {data.credits} credits
                  </div>
                </div>
              </div>
              
              {/* Time Label */}
              <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">
                {data.time}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center space-x-6 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-400 rounded-sm"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">API Calls</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-400 rounded-sm"></div>
          <span className="text-sm text-gray-600 dark:text-gray-400">Credits Used</span>
        </div>
      </div>
    </div>
  );
};

export default UsageChart;
