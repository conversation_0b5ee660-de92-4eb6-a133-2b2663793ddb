#!/usr/bin/env node

/**
 * Database Setup Script
 * 
 * This script sets up the PostgreSQL database with Prisma
 * Run this after installing dependencies and setting up environment variables
 * 
 * SECURITY: This script should ONLY be run server-side
 */

import { execSync } from 'child_process';
import { prisma } from '../libs/prisma.js';
import { cache } from '../libs/redis.js';

async function setupDatabase() {
  console.log('🚀 Starting database setup...');
  
  try {
    // Check if environment variables are set
    if (!process.env.POSTGRE_DB) {
      throw new Error('❌ POSTGRE_DB environment variable is not set');
    }
    
    console.log('📋 Generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    
    console.log('🔄 Pushing database schema...');
    execSync('npx prisma db push', { stdio: 'inherit' });
    
    console.log('🔍 Testing database connection...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Database connection successful');
    
    console.log('🔍 Testing Redis connection...');
    const redisHealth = await cache.ping();
    if (redisHealth) {
      console.log('✅ Redis connection successful');
    } else {
      console.log('⚠️  Redis connection failed - caching will be disabled');
    }
    
    console.log('🎉 Database setup completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Run: npm run dev');
    console.log('2. Visit: http://localhost:3000');
    console.log('3. Optional: Run "npm run db:studio" to view your database');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await cache.disconnect();
  }
}

// Run the setup
setupDatabase();
