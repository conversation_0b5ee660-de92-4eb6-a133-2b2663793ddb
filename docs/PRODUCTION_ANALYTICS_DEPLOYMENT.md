# Production Analytics Deployment Guide

## 🎯 Overview

The credit analytics system has been configured to **only collect data in production** to ensure realistic usage patterns. Development environments will skip data collection to avoid skewing the results.

## ✅ Pre-Deployment Checklist

### Database Preparation
- [x] **Analytics system installed** - Tables, functions, and views created
- [x] **Environment checks added** - Only runs when NODE_ENV != 'development'
- [x] **Analytics data reset** - Clean slate for production data collection
- [x] **Initial records created** - Ready for data collection

### Code Changes
- [x] **Database functions updated** - Check NODE_ENV before collecting data
- [x] **Admin endpoints updated** - Prevent data collection in development
- [x] **Dashboard script updated** - Shows environment warnings
- [x] **Database connection updated** - Automatically sets environment variable

## 🚀 Deployment Steps

### 1. **Set Production Environment**
On your production server, ensure NODE_ENV is set:

```bash
# In your production environment
export NODE_ENV=production

# Or in your .env file
echo "NODE_ENV=production" >> .env

# Verify it's set
echo $NODE_ENV  # Should output: production
```

### 2. **Deploy Code to Production**
```bash
# Commit all changes
git add .
git commit -m "Add production-only credit analytics system"
git push origin main

# Deploy to your production server
# (Use your normal deployment process)
```

### 3. **Verify Analytics Status**
After deployment, check that analytics collection is enabled:

```bash
# On production server, check analytics status
psql $DATABASE_URL -c "SELECT * FROM analytics_collection_status;"

# Should show:
# Analytics Collection Status | ENABLED (Production Environment)
# Stream Usage Analytics      | DATA AVAILABLE
# Credit Efficiency Analytics | NO DATA (will populate after usage)
```

### 4. **Test Data Collection**
```bash
# Manually trigger stats update (should work in production)
curl -X POST -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/update-stats"

# Should return success with environment: "production"
```

## 📊 Data Collection Process

### Automatic Collection
The system will automatically:
- **Track WebSocket stream usage** - Messages, users, credits consumed
- **Monitor tier efficiency** - Credit utilization by tier
- **Record tier migrations** - User upgrade/downgrade patterns
- **Calculate optimization recommendations** - Suggested credit costs

### Manual Collection
You can also manually update statistics:
```bash
# Update today's statistics
curl -X POST -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/update-stats"

# Update specific date
curl -X POST -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"date": "2024-01-15"}' \
  "https://your-domain.com/admin/analytics/update-stats"
```

## 🔍 Monitoring & Analysis

### Daily Dashboard
Run the analysis dashboard to see current insights:
```bash
# On production server
NODE_ENV=production node scripts/credit-analysis-dashboard.js
```

### API Endpoints
Access analytics data via admin endpoints:
```bash
# Get optimization recommendations
curl -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/credit-optimization"

# Get real-time usage
curl -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/realtime-usage"

# Get stream usage trends
curl -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/stream-usage?days=7"

# Get credit efficiency by tier
curl -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/credit-efficiency?days=7"

# Get comprehensive summary
curl -H "X-Admin-API-Key: $ADMIN_API_KEY" \
  "https://your-domain.com/admin/analytics/credit-summary?days=7"
```

## 📈 Expected Data Collection Timeline

### Day 1-2: **Initial Data**
- Basic usage patterns emerge
- Stream message counts recorded
- User engagement metrics collected

### Day 3-7: **Pattern Recognition**
- Daily averages stabilize
- Peak usage times identified
- Credit consumption patterns clear

### Week 2+: **Optimization Ready**
- Reliable recommendations available
- Tier efficiency analysis meaningful
- Credit cost optimization possible

## 🎯 Key Metrics to Monitor

### Stream Usage
- **Daily message volume** per stream
- **Unique users** per stream per day
- **Peak usage hours** and patterns
- **Credit consumption** trends

### Tier Efficiency
- **Credit utilization rates** by tier
- **User engagement** (active vs total users)
- **Tier migration patterns** (upgrades/downgrades)
- **Revenue impact** of credit changes

### Optimization Opportunities
- **Basic tier affordability** (days of usage possible)
- **Premium tier value** (full month coverage)
- **Enterprise tier efficiency** (resource utilization)
- **Recommended credit costs** for better balance

## 🚨 Troubleshooting

### Analytics Not Collecting
```bash
# Check environment setting
echo $NODE_ENV  # Should be 'production'

# Check database environment
psql $DATABASE_URL -c "SELECT current_setting('app.node_env', true);"

# Check analytics status
psql $DATABASE_URL -c "SELECT * FROM analytics_collection_status;"
```

### Development Environment Warning
If you see this error in development:
```json
{
  "error": "Analytics data collection is disabled in development environment",
  "message": "Deploy to production to collect realistic usage data"
}
```
This is **expected behavior** - analytics only run in production.

### No Data Available
If dashboard shows "No optimization data available":
- **Wait 24-48 hours** for meaningful data collection
- **Ensure users are actively using streams** in production
- **Check that WebSocket connections are working** properly

## 📋 Post-Deployment Checklist

### Week 1
- [ ] **Verify analytics collection** is working
- [ ] **Monitor daily usage patterns** via dashboard
- [ ] **Check for any errors** in logs
- [ ] **Ensure all streams** are being tracked

### Week 2
- [ ] **Run optimization analysis** for first recommendations
- [ ] **Review tier efficiency** metrics
- [ ] **Identify usage patterns** and peak times
- [ ] **Plan credit cost adjustments** based on data

### Week 3+
- [ ] **Implement recommended credit costs** if needed
- [ ] **Monitor user behavior changes** after adjustments
- [ ] **Track tier migration patterns** 
- [ ] **Optimize pricing strategy** based on insights

## 🎉 Success Indicators

### Data Collection Success
- ✅ **Analytics status shows "ENABLED"** in production
- ✅ **Daily usage data** being recorded
- ✅ **No errors** in analytics endpoints
- ✅ **Dashboard shows real data** instead of zeros

### Business Intelligence Success
- ✅ **Clear usage patterns** identified
- ✅ **Tier efficiency** metrics available
- ✅ **Optimization recommendations** generated
- ✅ **Credit costs** aligned with actual usage

## 📞 Support

If you encounter issues:
1. **Check environment variables** (NODE_ENV=production)
2. **Verify database connectivity** and permissions
3. **Review application logs** for errors
4. **Test analytics endpoints** manually
5. **Run dashboard script** for diagnostic information

---

**🚀 Ready for Production!** 

The analytics system is now configured to collect realistic usage data only in production. Deploy to your production server and start gathering insights for optimal credit pricing! 📊
