import { pubsub, cache } from "../config/redis.js";
import { Kafka } from "kafkajs";

const KAFKA_SSL = {
  ca: [process.env.KAFKA_CERT],
  key: [process.env.KAFKA_USER_KEY],
  cert: [process.env.KAFKA_USER_CERT],
  rejectUnauthorized: true,
};

const TOKENS = {
  SOL: "So11111111111111111111111111111111111111112",
  USDC: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
  USDT: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
};

export class KafkaStreams {
  constructor() {
    this.isRunning = false;
    this.kafka = new Kafka({
      clientId: process.env.KAFKA_CLIENT_ID,
      brokers: [process.env.KAFKA_BROKERS],
      ssl: KAFKA_SSL,
      connectionTimeout: 30000,
      authenticationTimeout: 30000,
      retry: {
        initialRetryTime: 100,
        retries: 100,
        maxRetryTime: 30000,
        factor: 2,
      },
    });
  }

  async init() {
    this.connect();
  }

  stop() {
    this.isRunning = false;
  }

  async connect() {
    if (this.isRunning) return;
    this.isRunning = true;
    this.consumer = this.kafka.consumer({
      groupId: `stalkapi-consumer-${Date.now()}`,
    });
    await this.consumer.connect();

    // subscribe to the different streams
    await Promise.all([
      this.consumer.subscribe({
        topic: "jupiter-amm-swaps",
        fromBeginning: false, // Only process new messages
      }),
      this.consumer.subscribe({
        topic: "pumpfun-amm-swaps",
        fromBeginning: false, // Only process new messages
      }),
      this.consumer.subscribe({
        topic: "stalkchain-dca-swaps",
        fromBeginning: false, // Only process new messages
      }),
    ]);

    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        let data = JSON.parse(message?.value?.toString());
        // console.log({
        //   topic,
        //   partition,
        //   message: {
        //     key: message.key?.toString(),
        //     value: message.value?.toString(),
        //     headers: message.headers,
        //   },
        // });

        const topic_convert = {
          "jupiter-amm-swaps": "jupiter-amm-swaps",
          "pumpfun-amm-swaps": "pumpfun-amm-swaps",
          "stalkchain-dca-swaps": "jupiter-dca-orders",
        }

        // Publish to pub/sub for real-time subscribers
        pubsub
          .publish("kafka_streams_internal", {
            stream: topic_convert[topic],
            data: data,
            timestamp: Date.now(),
          })
          // .then(() => {
          //   topic_convert[topic] === "jupiter-dca-orders" ? console.log(
          //     `✅ [KAFKA] Published message for ${
          //       topic
          //     }`
          //   ) : null;
          // })
          .catch((error) => {
            console.error("❌ [KOLFeed] Failed to publish message:", error);
          });
      },
    });
  }
}
