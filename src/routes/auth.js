import express from 'express';
import { User } from '../models/User.js';
import { verifyToken } from '../middleware/auth.js';
import { createEndpointRateLimit } from '../middleware/rateLimiter.js';
import { getCreditStatus } from '../middleware/credits.js';

const router = express.Router();

// Rate limiting for auth endpoints - temporarily disabled for debugging
// const authRateLimit = createEndpointRateLimit(10, 15); // 10 requests per 15 minutes

// Note: Registration and login are handled by the frontend website
// These endpoints have been removed for security reasons

// Note: Refresh token endpoint removed - API now uses only API key authentication



// Get usage statistics
router.get('/usage', verifyToken, async (req, res) => {
    try {
        const { days = 30 } = req.query;
        const user = await User.findById(req.user.id);
        const stats = await user.getUsageStats(parseInt(days));
        
        res.json({
            usage: stats,
            period: `${days} days`
        });
        
    } catch (error) {
        console.error('Usage stats error:', error);
        res.status(500).json({
            error: 'Failed to fetch usage statistics'
        });
    }
});

export default router;
