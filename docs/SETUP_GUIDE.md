# Setup Guide - Robust API Engine

This guide will walk you through setting up the Robust API Engine from scratch.

## 📋 Prerequisites

Before starting, ensure you have the following installed:

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **PostgreSQL 12+** - [Download here](https://www.postgresql.org/download/)
- **Redis 6+** - [Download here](https://redis.io/download)
- **pnpm** (recommended) or npm - `npm install -g pnpm`

## 🚀 Quick Setup

### 1. Install Dependencies

```bash
pnpm install
```

### 2. Setup PostgreSQL Database

```bash
# Create database (adjust credentials as needed)
createdb -U postgres api_engine

# Or using psql
psql -U postgres -c "CREATE DATABASE api_engine;"
```

### 3. Setup Redis

```bash
# Start Redis server (varies by OS)
# macOS with Homebrew:
brew services start redis

# Ubuntu/Debian:
sudo systemctl start redis-server

# Or run directly:
redis-server
```

### 4. Configure Environment

The `.env` file is already configured with development defaults. Update if needed:

```bash
# Edit .env file
nano .env
```

Key settings to verify:
- `DB_PASSWORD=postgres` (update with your PostgreSQL password)
- `JWT_SECRET` (change for production)
- Database and Redis connection details

### 5. Initialize Database

```bash
npm run db:migrate
```

This will:
- Create all necessary tables
- Set up indexes for performance
- Insert default access tiers
- Create demo user account
- Set up database functions

### 6. Start the Server

```bash
# Development mode (with auto-reload)
npm run dev

# Production mode
npm start
```

You should see:
```
🚀 Starting API Engine...
✅ Database connected successfully
✅ Redis connected successfully
✅ WebSocket server initialized
✅ All services initialized successfully
🌟 API Engine running on http://localhost:3000
🔌 WebSocket server available at ws://localhost:3000/ws
```

## 🧪 Testing the Setup

### 1. Basic API Test

```bash
npm run test:api
```

This will test:
- Health check endpoint
- Authentication
- Demo endpoints
- WebSocket info
- User profile

### 2. WebSocket Test

```bash
npm run test:websocket
```

This will test:
- WebSocket connection
- Stream subscription/unsubscription
- Real-time data streaming

### 3. Manual Testing

#### Health Check
```bash
curl http://localhost:3000/health
```

#### Login with Demo User
```bash
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"demo123"}'
```

## 🔧 Configuration Details

### Database Configuration

The system creates these main tables:
- `users` - User accounts and authentication
- `access_tiers` - Subscription tiers and limits
- `api_usage_logs` - Detailed usage tracking
- `websocket_sessions` - Active WebSocket connections
- `credit_transactions` - Credit purchase/usage history
- `stream_definitions` - Available data streams

### Access Tiers

| Tier | Credits/Month | Requests/Min | WebSocket Connections | Status |
|------|---------------|--------------|----------------------|--------|
| Free | 1,000 | 10 | 1 | Disabled |
| Basic | 10,000 | 60 | 3 | Active |
| Premium | 100,000 | 300 | 10 | Active |
| Enterprise | Unlimited | 1,000 | 50 | Active |

### Demo User Account

After migration, you'll have a demo user:
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Tier**: Basic (10,000 credits)
- **API Key**: Generated automatically

## 🌐 API Endpoints

### Authentication
- `POST /auth/register` - Create new user
- `POST /auth/login` - User login
- `POST /auth/refresh` - Refresh JWT token

### API Endpoints
- `GET /api/v1/status` - Public status check
- `GET /api/v1/demo` - Demo endpoint (1 credit)
- `GET /api/v1/data` - Data endpoint (2 credits)
- `GET /api/v1/analytics` - Analytics (5 credits, Premium+)
- `GET /api/v1/search` - Search endpoint (2 credits)
- `POST /api/v1/submit` - Submit data (3 credits)
- `GET /api/v1/kol-feed/history` - KOL trading history (3 credits)
- `POST /api/v1/batch` - Batch processing (10 credits, Enterprise)

### WebSocket Management
- `GET /ws-api/info` - WebSocket connection info
- `GET /ws-api/streams` - Available streams
- `GET /ws-api/sessions` - Active sessions
- `GET /ws-api/stats` - Usage statistics

## 🔌 WebSocket Streams

### Available Streams
- **kol-feed** - Real-time KOL trading activity (Basic+, 2 credits per message)
- **jupiter-amm-swaps** - Real-time Jupiter AMM swap data (Enterprise, 0 credits per message)
- **pumpfun-amm-swaps** - Real-time Pump.fun AMM swap data (Enterprise, 0 credits per message)
- **jupiter-dca-orders** - Real-time Jupiter DCA order data (Enterprise, 0 credits per message)

### Connection Examples

```javascript
// Using API key (recommended)
const ws = new WebSocket('ws://localhost:3000/ws?apiKey=YOUR_API_KEY');

// Subscribe to KOL feed stream
ws.send(JSON.stringify({
  type: 'subscribe',
  payload: { stream: 'kol-feed' }
}));

// Subscribe to Enterprise streams (requires Enterprise tier)
ws.send(JSON.stringify({
  type: 'subscribe',
  payload: { stream: 'jupiter-amm-swaps' }
}));
```

## 🛠️ Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check PostgreSQL is running
pg_isready -U postgres

# Check database exists
psql -U postgres -l | grep api_engine

# Recreate database if needed
dropdb -U postgres api_engine
createdb -U postgres api_engine
npm run db:migrate
```

#### Redis Connection Failed
```bash
# Check Redis is running
redis-cli ping

# Should return: PONG

# Start Redis if not running
redis-server
```

#### Port Already in Use
```bash
# Check what's using port 3000
lsof -i :3000

# Kill the process or change PORT in .env
```

#### Migration Errors
```bash
# Reset database
psql -U postgres -c "DROP DATABASE IF EXISTS api_engine;"
psql -U postgres -c "CREATE DATABASE api_engine;"
npm run db:migrate
```

### Logs and Debugging

#### Enable Debug Logging
```bash
# Set in .env
LOG_LEVEL=debug
NODE_ENV=development
```

#### Check Application Logs
The application logs to console in development mode. Key things to look for:
- Database connection status
- Redis connection status
- WebSocket server initialization
- Stream manager startup

#### Database Queries
In development mode, all database queries are logged with execution time.

## 🔒 Security Considerations

### Development vs Production

#### Development (Current Setup)
- Weak JWT secret (change for production)
- CORS allows localhost
- Detailed error messages
- Debug logging enabled

#### Production Recommendations
- Strong JWT secret (32+ random characters)
- Restrict CORS origins
- Disable debug logging
- Use environment-specific database credentials
- Enable SSL for database connections
- Use Redis AUTH
- Set up proper firewall rules

### Environment Variables for Production
```bash
NODE_ENV=production
JWT_SECRET=your-super-secure-random-secret-key-here
DB_SSL=true
REDIS_PASSWORD=your-redis-password
LOG_LEVEL=warn
```

## 📈 Performance Optimization

### Database
- Indexes are automatically created during migration
- Connection pooling is configured (max 20 connections)
- Use read replicas for analytics queries in production

### Redis
- Caching is enabled for user data, analytics, and search results
- Pub/sub is used for real-time WebSocket communication
- Consider Redis Cluster for high availability

### Application
- Compression middleware enabled
- Rate limiting prevents abuse
- Graceful shutdown handles cleanup properly

## 🚀 Next Steps

1. **Customize Access Tiers**: Modify the tiers in `SQL/03_insert_default_data.sql`
2. **Add Custom Endpoints**: Create new routes in `src/routes/`
3. **Implement Payment Integration**: Add payment processing for credit purchases
4. **Add More Streams**: Extend the StreamManager with your data sources
5. **Set Up Monitoring**: Add application monitoring and alerting
6. **Deploy to Production**: Use Docker, PM2, or your preferred deployment method

## 📚 Additional Resources

- [API Documentation](docs/API_DOCUMENTATION.md)
- [README](README.md)
- [Database Schema](SQL/)

For support, check the health endpoint and application logs first, then refer to the troubleshooting section above.
