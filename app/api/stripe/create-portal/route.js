import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/libs/next-auth";
import { prisma } from "@/libs/prisma";
import { cache, CacheKeys } from "@/libs/redis";
import { createCustomerPortal } from "@/libs/stripe";

export async function POST(req) {
  const session = await getServerSession(authOptions);

  if (session) {
    try {
      const body = await req.json();

      const { id } = session.user;

      // Check cache first
      const userCacheKey = CacheKeys.user(id);
      let user = await cache.get(userCacheKey);

      if (!user) {
        user = await prisma.user.findUnique({
          where: { id }
        });

        if (user) {
          // Cache user for 1 hour
          await cache.set(userCacheKey, user, 3600);
        }
      }

      if (!user?.customerId) {
        return NextResponse.json(
          {
            error:
              "You don't have a billing account yet. Make a purchase first.",
          },
          { status: 400 }
        );
      } else if (!body.returnUrl) {
        return NextResponse.json(
          { error: "Return URL is required" },
          { status: 400 }
        );
      }

      const stripePortalUrl = await createCustomerPortal({
        customerId: user.customerId,
        returnUrl: body.returnUrl,
      });

      return NextResponse.json({
        url: stripePortalUrl,
      });
    } catch (e) {
      console.error(e);
      return NextResponse.json({ error: e?.message }, { status: 500 });
    }
  } else {
    // Not Signed in
    return NextResponse.json({ error: "Not signed in" }, { status: 401 });
  }
}
