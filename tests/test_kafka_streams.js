import { WebSocket } from 'ws';
import axios from 'axios';

// Demo user API key from the database (Note: This user needs Enterprise tier for Kafka streams)
const API_KEY = '2fF24O8HUvmHYAOUzPigSJc3acLMDS4LbGe9Gg54OfRWScE4OG';
const WS_URL = `ws://localhost:3001/ws?apiKey=${API_KEY}`;
const BASE_URL = 'http://localhost:3001';

console.log('🔌 Testing Kafka Streams Integration...');

// Test 1: WebSocket Connection and Subscription
console.log('\n1. Testing WebSocket connection and stream subscriptions...');
const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('✅ Connected to WebSocket');

    // Wait a bit for the connection to be fully established
    setTimeout(() => {
        // Subscribe to all Kafka streams
        const streams = ['jupiter-amm-swaps', 'pumpfun-amm-swaps', 'jupiter-dca-orders'];
        
        streams.forEach(stream => {
            const subscribeMessage = {
                type: 'subscribe',
                payload: {
                    stream: stream
                }
            };

            console.log(`📡 Subscribing to ${stream} stream...`);
            ws.send(JSON.stringify(subscribeMessage));
        });
    }, 1000);
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'stream_data') {
            const streamName = message.stream;
            const eventType = message.data?.type || 'unknown';
            console.log(`🎯 [${streamName.toUpperCase()}] Received ${eventType} event`);
            
            // Log first few events in detail for debugging
            if (Math.random() < 0.1) { // 10% chance to log details
                console.log(`📊 Sample data:`, JSON.stringify(message.data, null, 2));
            }
        } else if (message.type === 'error') {
            console.log(`❌ WebSocket Error: ${message.error}`);
        } else if (message.type === 'subscription_confirmed') {
            console.log(`✅ Subscription confirmed for: ${message.stream}`);
        }
    } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
        console.log('Raw message:', data.toString());
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed: ${code} - ${reason}`);
});

// Test 2: History API Endpoints
console.log('\n2. Testing Kafka streams history endpoints...');

async function testHistoryEndpoints() {
    const endpoints = [
        '/api/v1/jupiter-amm-swaps/history',
        '/api/v1/pumpfun-amm-swaps/history',
        '/api/v1/jupiter-dca-orders/history'
    ];

    for (const endpoint of endpoints) {
        try {
            console.log(`\n📊 Testing ${endpoint}...`);
            const response = await axios.get(`${BASE_URL}${endpoint}?limit=5`, {
                headers: { 'X-API-Key': API_KEY }
            });
            
            console.log(`   ✅ ${endpoint} - Status: ${response.status}`);
            console.log(`   📈 Returned ${response.data.data.length} items`);
            console.log(`   📊 Total in history: ${response.data.pagination.total}`);
            console.log(`   💳 Credits consumed: ${response.data.credits_consumed}`);
            
            // Log sample data if available
            if (response.data.data.length > 0) {
                const sample = response.data.data[0];
                console.log(`   🔍 Sample event type: ${sample.type || 'unknown'}`);
                console.log(`   ⏰ Sample timestamp: ${new Date(sample.timestamp).toISOString()}`);
            }
            
        } catch (error) {
            if (error.response?.status === 403) {
                console.log(`   ⚠️  ${endpoint} - Access denied (Enterprise tier required)`);
            } else if (error.response?.status === 402) {
                console.log(`   ⚠️  ${endpoint} - Insufficient credits`);
            } else {
                console.log(`   ❌ ${endpoint} - Error: ${error.message}`);
                if (error.response?.data) {
                    console.log(`   📄 Response: ${JSON.stringify(error.response.data)}`);
                }
            }
        }
    }
}

// Test 3: Stream Information
console.log('\n3. Testing stream information endpoints...');

async function testStreamInfo() {
    try {
        console.log('\n📋 Testing /ws-api/streams endpoint...');
        const response = await axios.get(`${BASE_URL}/ws-api/streams`, {
            headers: { 'X-API-Key': API_KEY }
        });
        
        console.log('✅ Available streams:');
        response.data.streams.forEach(stream => {
            console.log(`   - ${stream.name}: ${stream.description}`);
            console.log(`     Tier: ${stream.tier_required}, Credits: ${stream.credit_cost}`);
        });
        
    } catch (error) {
        console.log(`❌ Stream info error: ${error.message}`);
    }
}

// Test 4: User Access Check
console.log('\n4. Testing user access and tier information...');

async function testUserAccess() {
    try {
        console.log('\n👤 Testing user access information...');
        const response = await axios.get(`${BASE_URL}/ws-api/info`, {
            headers: { 'X-API-Key': API_KEY }
        });
        
        const wsInfo = response.data.websocket;
        console.log('✅ User WebSocket info:');
        console.log(`   Max connections: ${wsInfo.connection_limits?.max_connections}`);
        console.log(`   Current tier: ${wsInfo.connection_limits?.current_tier}`);
        console.log(`   Available streams: ${wsInfo.available_streams?.join(', ')}`);
        
    } catch (error) {
        console.log(`❌ User access error: ${error.message}`);
    }
}

// Run tests
setTimeout(async () => {
    await testHistoryEndpoints();
    await testStreamInfo();
    await testUserAccess();
    
    console.log('\n🎯 Kafka Streams test completed!');
    console.log('📝 Note: For full testing, ensure the user has Enterprise tier access');
    console.log('🔄 WebSocket will continue listening for messages...');
    console.log('Press Ctrl+C to exit');
}, 3000);

// Keep the process alive and handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Closing WebSocket connection...');
    ws.close();
    process.exit(0);
});

console.log('🎯 Waiting for Kafka stream messages...');
console.log('📊 This test requires Enterprise tier access for full functionality');
console.log('Press Ctrl+C to exit');
