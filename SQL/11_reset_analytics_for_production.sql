-- Reset Analytics Data for Production Data Collection
-- This script clears all analytics data to start fresh on production server

-- Clear all analytics tables
TRUNCATE TABLE stream_usage_analytics RESTART IDENTITY CASCADE;
TRUNCATE TABLE credit_efficiency_log RESTART IDENTITY CASCADE;
TRUNCATE TABLE tier_migration_log RESTART IDENTITY CASCADE;

-- Clear existing API usage logs to start fresh (optional - comment out if you want to keep historical data)
-- TRUNCATE TABLE api_usage_logs RESTART IDENTITY CASCADE;

-- Reset initial data for current streams with today's date
INSERT INTO stream_usage_analytics (stream_name, date, total_messages, unique_subscribers, total_credits_consumed)
VALUES 
    ('kol-feed', CURRENT_DATE, 0, 0, 0),
    ('jupiter-amm-swaps', CURRENT_DATE, 0, 0, 0),
    ('pumpfun-amm-swaps', CURRENT_DATE, 0, 0, 0),
    ('jupiter-dca-orders', CURRENT_DATE, 0, 0, 0)
ON CONFLICT (stream_name, date) DO UPDATE SET
    total_messages = 0,
    unique_subscribers = 0,
    total_credits_consumed = 0,
    updated_at = CURRENT_TIMESTAMP;

-- Create a function to automatically update stats daily (for production cron job)
CREATE OR REPLACE FUNCTION auto_update_daily_analytics()
RETURNS void AS $$
BEGIN
    -- Only run in production
    IF current_setting('app.node_env', true) != 'development' THEN
        PERFORM update_daily_stream_stats();
        PERFORM update_credit_efficiency_stats();
        
        RAISE NOTICE 'Daily analytics updated automatically for %', CURRENT_DATE;
    ELSE
        RAISE NOTICE 'Skipping automatic analytics update in development environment';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create a view to show analytics collection status
CREATE OR REPLACE VIEW analytics_collection_status AS
SELECT 
    'Analytics Collection Status' as component,
    CASE 
        WHEN current_setting('app.node_env', true) = 'development' 
        THEN 'DISABLED (Development Environment)'
        ELSE 'ENABLED (Production Environment)'
    END as status,
    COUNT(*) as total_records,
    MAX(date) as latest_data_date,
    MIN(date) as earliest_data_date
FROM stream_usage_analytics
UNION ALL
SELECT 
    'Stream Usage Analytics' as component,
    CASE 
        WHEN COUNT(*) > 0 THEN 'DATA AVAILABLE'
        ELSE 'NO DATA'
    END as status,
    COUNT(*) as total_records,
    MAX(date) as latest_data_date,
    MIN(date) as earliest_data_date
FROM stream_usage_analytics
UNION ALL
SELECT 
    'Credit Efficiency Analytics' as component,
    CASE 
        WHEN COUNT(*) > 0 THEN 'DATA AVAILABLE'
        ELSE 'NO DATA'
    END as status,
    COUNT(*) as total_records,
    MAX(date) as latest_data_date,
    MIN(date) as earliest_data_date
FROM credit_efficiency_log;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE '✅ Analytics data reset complete!';
    RAISE NOTICE '📊 Ready for production data collection';
    RAISE NOTICE '🚀 Deploy to production and analytics will start automatically';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Set NODE_ENV=production on your production server';
    RAISE NOTICE '2. Deploy this code to production';
    RAISE NOTICE '3. Analytics will automatically collect realistic usage data';
    RAISE NOTICE '4. Run the dashboard after 24-48 hours for initial insights';
    RAISE NOTICE '';
    RAISE NOTICE 'Check status: SELECT * FROM analytics_collection_status;';
END $$;
