# Authentication Configuration Guide

This guide explains how to easily enable/disable different authentication methods in your StalkAPI application.

## Quick Setup

### 1. Configure Authentication Providers

In `config.js`, you'll find the `enabledAuth` object:

```javascript
enabledAuth: {
  google: true,        // Enable/disable Google OAuth login
  email: false,        // Enable/disable email magic link login
  // Add more providers here as needed:
  // github: false,
  // discord: false,
  // apple: false,
},
```

### 2. Enable/Disable Providers

Simply change the boolean values:

- **To hide email login**: Set `email: false` (currently set)
- **To hide Google login**: Set `google: false`
- **To enable email login**: Set `email: true`

### 3. Current Configuration

**Currently Active:**
- ✅ Google OAuth (enabled)
- ❌ Email Magic Links (disabled)

**To hide email login completely**, the current setting `email: false` will:
- Remove the email input field from the signin page
- Remove the "Sign in with <PERSON><PERSON>" button
- Remove the "or" divider between Google and email options
- Only show the Google signin button

## How It Works

### Backend (NextAuth Configuration)
The `libs/next-auth.js` file automatically reads your `enabledAuth` configuration and only includes the providers you've enabled:

```javascript
// Google provider only loads if enabled AND environment variables are set
...(config.enabledAuth.google && process.env.GOOGLE_ID && process.env.GOOGLE_SECRET
  ? [GoogleProvider({ ... })]
  : []),

// Email provider only loads if enabled AND database/SMTP are configured
...(config.enabledAuth.email && process.env.POSTGRE_DB && process.env.SMTP_HOST
  ? [EmailProvider({ ... })]
  : []),
```

### Helper Functions

You can use these helper functions in your components:

```javascript
import { getEnabledAuthProviders, isAnyAuthEnabled, isAuthProviderEnabled } from '@/config';

// Get list of enabled providers
const enabledProviders = getEnabledAuthProviders(); // ['google']

// Check if any auth is enabled
const hasAuth = isAnyAuthEnabled(); // true

// Check specific provider
const hasGoogle = isAuthProviderEnabled('google'); // true
const hasEmail = isAuthProviderEnabled('email'); // false
```

## Adding New Providers

To add new authentication providers (GitHub, Discord, Apple, etc.):

1. **Add to config:**
```javascript
enabledAuth: {
  google: true,
  email: false,
  github: false,    // Add new provider
  discord: false,   // Add new provider
},
```

2. **Install the provider:**
```bash
npm install next-auth/providers/github
```

3. **Add to NextAuth config:**
```javascript
import GitHubProvider from "next-auth/providers/github";

// In providers array:
...(config.enabledAuth.github && process.env.GITHUB_ID && process.env.GITHUB_SECRET
  ? [GitHubProvider({
      clientId: process.env.GITHUB_ID,
      clientSecret: process.env.GITHUB_SECRET,
    })]
  : []),
```

4. **Add environment variables:**
```env
GITHUB_ID=your_github_client_id
GITHUB_SECRET=your_github_client_secret
```

## Environment Variables Required

### For Google OAuth:
```env
GOOGLE_ID=your_google_client_id
GOOGLE_SECRET=your_google_client_secret
```

### For Email Magic Links:
```env
POSTGRE_DB=your_database_url
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=<EMAIL>
```

## Current Status

With `email: false`, your signin page will only show:
- Google "Sign in with Google" button
- No email input field
- No "Sign in with Email" button
- Clean, simple interface

This is perfect for when you want to streamline the login process and only offer OAuth providers.
