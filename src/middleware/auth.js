import { query } from '../config/database.js';
import { cache } from '../config/redis.js';

// Verify API key authentication
export const verifyToken = async (req, res, next) => {
    try {
        const apiKey = req.headers['x-api-key'] || req.query.apiKey;

        if (!apiKey) {
            return res.status(401).json({ error: 'API key required' });
        }

        // Check cache first
        const cacheKey = `apikey:${apiKey}`;
        let user = await cache.get(cacheKey);

        if (!user) {
            // Fetch from database
            const result = await query(
                `SELECT u.*, at.name as tier_name, at.max_credits_per_month,
                        at.max_requests_per_minute, at.max_websocket_connections,
                        at.allowed_endpoints, at.allowed_streams
                 FROM users u
                 JOIN access_tiers at ON u.tier_id = at.id
                 WHERE u.api_key = $1 AND u.is_active = true`,
                [apiKey]
            );

            if (result.rows.length === 0) {
                return res.status(401).json({ error: 'Invalid API key' });
            }

            user = result.rows[0];
            // Cache user data for 5 minutes
            await cache.set(cacheKey, user, 300);
        }

        req.user = user;
        req.authMethod = 'apikey';

        next();
    } catch (error) {
        console.error('Auth middleware error:', error);
        res.status(500).json({ error: 'Authentication error' });
    }
};

// Check if user has access to specific endpoint
export const checkEndpointAccess = (endpoint) => {
    return (req, res, next) => {
        try {
            const user = req.user;
            const allowedEndpoints = user.allowed_endpoints;
            
            // Check if user has access to all endpoints (*)
            if (allowedEndpoints.includes('*')) {
                return next();
            }
            
            // Check if specific endpoint is allowed
            if (allowedEndpoints.includes(endpoint)) {
                return next();
            }
            
            // Check for wildcard patterns
            const hasWildcardAccess = allowedEndpoints.some(pattern => {
                if (pattern.endsWith('*')) {
                    const prefix = pattern.slice(0, -1);
                    return endpoint.startsWith(prefix);
                }
                return false;
            });
            
            if (hasWildcardAccess) {
                return next();
            }
            
            return res.status(403).json({ 
                error: 'Access denied - insufficient tier permissions',
                required_tier: 'Higher tier required for this endpoint'
            });
        } catch (error) {
            console.error('Endpoint access check error:', error);
            res.status(500).json({ error: 'Access check error' });
        }
    };
};

// Check if user has access to specific stream
export const checkStreamAccess = (streamName) => {
    return (req, res, next) => {
        try {
            const user = req.user;
            const allowedStreams = user.allowed_streams;
            
            // Check if user has access to all streams (*)
            if (allowedStreams.includes('*')) {
                return next();
            }
            
            // Check if specific stream is allowed
            if (allowedStreams.includes(streamName)) {
                return next();
            }
            
            return res.status(403).json({ 
                error: 'Access denied - insufficient tier permissions for stream',
                stream: streamName,
                required_tier: 'Higher tier required for this stream'
            });
        } catch (error) {
            console.error('Stream access check error:', error);
            res.status(500).json({ error: 'Stream access check error' });
        }
    };
};

// Check if user has sufficient credits
export const checkCredits = (requiredCredits = 1) => {
    return (req, res, next) => {
        try {
            const user = req.user;
            
            // Unlimited tier (-1) always has access
            if (user.max_credits_per_month === -1) {
                req.creditsRequired = requiredCredits;
                return next();
            }
            
            // Check if user has enough credits
            if (user.credits_remaining < requiredCredits) {
                return res.status(402).json({ 
                    error: 'Insufficient credits',
                    credits_remaining: user.credits_remaining,
                    credits_required: requiredCredits
                });
            }
            
            req.creditsRequired = requiredCredits;
            next();
        } catch (error) {
            console.error('Credits check error:', error);
            res.status(500).json({ error: 'Credits check error' });
        }
    };
};

// Optional authentication (doesn't fail if no auth provided)
export const optionalAuth = async (req, res, next) => {
    try {
        const apiKey = req.headers['x-api-key'] || req.query.apiKey;

        if (!apiKey) {
            req.user = null;
            return next();
        }

        // Use the same logic as verifyToken but don't fail
        await verifyToken(req, res, next);
    } catch (error) {
        // If auth fails, continue without user
        req.user = null;
        next();
    }
};
