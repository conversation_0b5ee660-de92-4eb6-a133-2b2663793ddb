{"openapi": "3.0.3", "info": {"title": "StalkAPI", "description": "StalkAPI provides API access to StalkChain data and analytics. Data is available through both REST API and WebSocket streaming.\n\n## Authentication\nStalkAPI uses API key authentication for all endpoints and WebSocket connections:\n- **API Key**: Generated for each user account and used for all authentication\n- **Header**: `X-API-Key: YOUR_API_KEY`\n- **Query Parameter**: `?apiKey=YOUR_API_KEY`\n\n## Credit System\nMost endpoints consume credits based on your tier:\n- **Free Tier**: 1,000 credits/month\n- **Basic Tier**: 10,000 credits/month\n- **Premium Tier**: 100,000 credits/month\n- **Enterprise Tier**: Unlimited credits\n\n## Rate Limiting\nAPI endpoints are rate-limited based on your tier to ensure fair usage.\n\n## WebSocket Connection\nReal-time data is available through WebSocket connections at `wss://data.stalkapi.com/ws`\n\n### WebSocket Connection\nConnect to the WebSocket endpoint using your API key:\n```javascript\nconst ws = new WebSocket('wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY');\n```\n\n### Available Streams\n- **kol-feed**: Real-time KOL trading activity (Basic+, 2 credits per message)\n- **jupiter-amm-swaps**: Real-time Jupiter AMM swap data (Enterprise, 0 credits per message)\n- **pumpfun-amm-swaps**: Real-time Pump.fun AMM swap data (Enterprise, 0 credits per message)\n- **jupiter-dca-orders**: Real-time Jupiter DCA order data (Enterprise, 0 credits per message)\n\n### Message Protocol\nSubscribe to streams by sending JSON messages:\n```json\n{\n  \"type\": \"subscribe\",\n  \"payload\": {\n    \"stream\": \"kol-feed\"\n  }\n}\n```\n", "version": "1.0.0", "contact": {"name": "StalkAPI Support", "url": "https://stalkapi.com", "email": "<EMAIL>"}, "license": {"name": "Proprietary", "url": "https://stalkapi.com/terms"}}, "servers": [{"url": "https://data.stalkapi.com", "description": "Production server"}, {"url": "http://localhost:3000", "description": "Development server"}], "security": [{"ApiKeyAuth": []}], "tags": [{"name": "WebSocket Info", "description": "WebSocket connection information and management"}, {"name": "Core Endpoints", "description": "Core system and authentication endpoints"}, {"name": "Historical Data KOL", "description": "KOL trading data and historical information"}, {"name": "Historical Data Kafka", "description": "Kafka stream data and historical information (Enterprise tier)"}], "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "API key for authentication. Can be provided as query parameter (?apiKey=YOUR_KEY) or X-API-Key header."}}, "schemas": {"Error": {"type": "object", "properties": {"error": {"type": "string", "description": "Error message"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}, "required": ["error"]}, "User": {"type": "object", "properties": {"id": {"type": "integer", "description": "User ID"}, "email": {"type": "string", "format": "email", "description": "User email"}, "tier_name": {"type": "string", "description": "User tier (free, basic, premium, enterprise)"}, "credits_remaining": {"type": "integer", "description": "Remaining credits"}, "max_websocket_connections": {"type": "integer", "description": "Maximum allowed WebSocket connections"}, "allowed_streams": {"type": "array", "items": {"type": "string"}, "description": "List of streams user has access to"}}}, "KOLFeedItem": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the feed item"}, "timestamp": {"type": "string", "format": "date-time", "description": "When the trading activity occurred"}, "kol_name": {"type": "string", "description": "Name of the KOL"}, "action": {"type": "string", "enum": ["buy", "sell"], "description": "Trading action"}, "token": {"type": "string", "description": "Token symbol"}, "amount": {"type": "number", "description": "Amount traded"}, "price": {"type": "number", "description": "Price at time of trade"}, "market_cap": {"type": "number", "description": "Market cap at time of trade"}}}, "WebSocketInfo": {"type": "object", "properties": {"endpoint": {"type": "string", "description": "WebSocket endpoint URL", "example": "wss://data.stalkapi.com/ws"}, "protocols": {"type": "array", "items": {"type": "string"}, "description": "Supported protocols"}, "authentication": {"type": "object", "properties": {"method": {"type": "string", "description": "Authentication method", "example": "API Key"}, "parameter": {"type": "string", "description": "Parameter name for API key", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "example_url": {"type": "string", "description": "Example connection URL", "example": "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"}}}, "connection_limits": {"type": "object", "properties": {"max_connections": {"type": "integer", "description": "Maximum connections for user's tier"}, "current_tier": {"type": "string", "description": "User's current tier"}}}, "available_streams": {"type": "array", "items": {"type": "string"}, "description": "Streams available to user"}}}, "KafkaStreamItem": {"type": "object", "properties": {"timestamp": {"type": "number", "description": "Unix timestamp when the event occurred"}, "source": {"type": "string", "description": "Source stream name", "enum": ["jupiter-amm-swaps", "pumpfun-amm-swaps", "jupiter-dca-orders"]}, "type": {"type": "string", "description": "Event type", "enum": ["jupiter_amm_swap", "pumpfun_amm_swap", "jupiter_dca_order"]}, "data": {"type": "object", "description": "Raw event data from Kafka stream"}}}}}, "paths": {"/": {"get": {"tags": ["Core Endpoints"], "summary": "API Root", "description": "Get basic API information and WebSocket endpoint", "security": [], "responses": {"200": {"description": "API information", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "StalkApi"}, "version": {"type": "string", "example": "1.0.0"}, "timestamp": {"type": "string", "format": "date-time"}, "websocket": {"type": "object", "properties": {"endpoint": {"type": "string", "example": "wss://data.stalkapi.com/ws"}, "authentication": {"type": "string", "example": "API key required"}, "example_connection": {"type": "string", "example": "wss://data.stalkapi.com/ws?apiKey=YOUR_API_KEY"}}}}}}}}}}}, "/api/v1/kol-feed/history": {"get": {"tags": ["Historical Data KOL"], "summary": "KOL Feed", "description": "Get historical KOL trading data (requires Basic tier or higher, consumes 3 credits)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 100)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "KOL feed history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KOLFeedItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 3}, "message": {"type": "string", "example": "KOL feed history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/jupiter-amm-swaps/history": {"get": {"tags": ["Historical Data Kafka"], "summary": "Jupiter AMM Swaps History", "description": "Get historical Jupiter AMM swap data (requires Enterprise tier, consumes 5 credits)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 1000)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "Jupiter AMM swaps history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KafkaStreamItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 5}, "message": {"type": "string", "example": "Jupiter AMM swaps history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/pumpfun-amm-swaps/history": {"get": {"tags": ["Historical Data Kafka"], "summary": "Pump.fun AMM Swaps History", "description": "Get historical Pump.fun AMM swap data (requires Enterprise tier, consumes 5 credits)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 1000)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "Pump.fun AMM swaps history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KafkaStreamItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 5}, "message": {"type": "string", "example": "Pump.fun AMM swaps history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v1/jupiter-dca-orders/history": {"get": {"tags": ["Historical Data Kafka"], "summary": "Jupiter DCA Orders History", "description": "Get historical Jupiter DCA order data (requires Enterprise tier, consumes 5 credits)", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "limit", "in": "query", "description": "Number of items to return (max 1000)", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 1000, "default": 100}}, {"name": "offset", "in": "query", "description": "Number of items to skip", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}], "responses": {"200": {"description": "Jupiter DCA orders history data", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/KafkaStreamItem"}}, "pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "total": {"type": "integer"}, "returned": {"type": "integer"}}}, "credits_consumed": {"type": "integer", "example": 5}, "message": {"type": "string", "example": "Jupiter DCA orders history retrieved successfully"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "403": {"description": "Insufficient credits or tier access", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/info": {"get": {"tags": ["WebSocket Info"], "summary": "WebSocket Connection Info", "description": "Get WebSocket server information and connection details", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "WebSocket connection information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "websocket": {"$ref": "#/components/schemas/WebSocketInfo"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/streams": {"get": {"tags": ["WebSocket Info"], "summary": "Available Streams", "description": "Get list of streams available to the authenticated user", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Available streams", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "streams": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Stream name"}, "description": {"type": "string", "description": "Stream description"}, "tier_required": {"type": "string", "description": "Minimum tier required"}, "credit_cost": {"type": "integer", "description": "Credits consumed per connection"}}}}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/sessions": {"get": {"tags": ["WebSocket Info"], "summary": "Active Sessions", "description": "Get information about user's active WebSocket sessions", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Active sessions information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "sessions": {"type": "array", "items": {"type": "object", "properties": {"session_id": {"type": "string", "description": "Session identifier"}, "connection_id": {"type": "string", "description": "Connection identifier"}, "connected_at": {"type": "string", "format": "date-time", "description": "Connection timestamp"}, "subscriptions": {"type": "array", "items": {"type": "string"}, "description": "Active stream subscriptions"}, "ip_address": {"type": "string", "description": "Client IP address"}}}}, "total_connections": {"type": "integer", "description": "Total active connections"}, "max_connections": {"type": "integer", "description": "Maximum allowed connections"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/stats": {"get": {"tags": ["WebSocket Info"], "summary": "Usage Statistics", "description": "Get WebSocket usage statistics for the authenticated user", "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Usage statistics", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "stats": {"type": "object", "properties": {"total_connections": {"type": "integer", "description": "Total connections made"}, "total_messages": {"type": "integer", "description": "Total messages received"}, "credits_consumed": {"type": "integer", "description": "Total credits consumed via WebSocket"}, "active_streams": {"type": "array", "items": {"type": "string"}, "description": "Currently active streams"}}}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/ws-api/credits": {"get": {"tags": ["WebSocket Info"], "summary": "Stream Credit Information", "description": "Get credit costs and information for WebSocket streams", "security": [{"ApiKeyAuth": []}], "parameters": [{"name": "stream", "in": "query", "description": "Specific stream to get credit information for", "required": false, "schema": {"type": "string", "example": "kol-feed"}}], "responses": {"200": {"description": "Stream credit information", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "credits": {"type": "object", "properties": {"stream_costs": {"type": "object", "description": "Credit costs per stream", "additionalProperties": {"type": "integer"}}, "user_credits": {"type": "integer", "description": "User's remaining credits"}, "billing_period": {"type": "string", "description": "Current billing period"}}}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}}