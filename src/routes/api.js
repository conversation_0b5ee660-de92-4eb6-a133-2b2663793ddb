import express from 'express';
import { verifyToken, checkEndpointAccess, checkCredits } from '../middleware/auth.js';
import { consumeCredits, responseTime } from '../middleware/credits.js';
import { createUserRateLimit } from '../middleware/rateLimiter.js';
import { cache } from '../config/redis.js';

const router = express.Router();

// Apply rate limiting to all API routes - temporarily disabled for debugging
// router.use(createUserRateLimit());

// Apply response time tracking
router.use(responseTime);

// Public status endpoint (no auth required)
router.get('/status', async (req, res) => {
    try {
        const status = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development'
        };
        
        res.json(status);
    } catch (error) {
        res.status(500).json({
            error: 'Status check failed',
            timestamp: new Date().toISOString()
        });
    }
});









// KOL Feed History endpoint - requires basic tier or higher
router.get('/kol-feed/history',
    verifyToken,
    checkEndpointAccess('/api/v1/kol-feed/history'),
    checkCredits(3),
    consumeCredits(3),
    async (req, res) => {
        try {
            const { limit = 100, offset = 0 } = req.query;
            const maxLimit = 100;
            const requestedLimit = Math.min(parseInt(limit), maxLimit);
            const requestedOffset = Math.max(parseInt(offset), 0);

            // Get KOL feed history from Redis
            const historyData = await cache.lrange('kol_feed_history', requestedOffset, requestedOffset + requestedLimit - 1);
            const totalCount = await cache.llen('kol_feed_history');

            res.json({
                success: true,
                data: historyData,
                pagination: {
                    limit: requestedLimit,
                    offset: requestedOffset,
                    total: totalCount,
                    returned: historyData.length
                },
                credits_consumed: 3,
                message: 'KOL feed history retrieved successfully'
            });

        } catch (error) {
            console.error('KOL feed history endpoint error:', error);
            res.status(500).json({
                error: 'Failed to retrieve KOL feed history'
            });
        }
    }
);

// Jupiter AMM Swaps History endpoint - requires enterprise tier
router.get('/jupiter-amm-swaps/history',
    verifyToken,
    checkEndpointAccess('/api/v1/jupiter-amm-swaps/history'),
    checkCredits(0),
    consumeCredits(0),
    async (req, res) => {
        try {
            const { limit = 100, offset = 0 } = req.query;
            const maxLimit = 1000; // Higher limit for enterprise users
            const requestedLimit = Math.min(parseInt(limit), maxLimit);
            const requestedOffset = Math.max(parseInt(offset), 0);

            // Get Jupiter AMM swaps history from Redis
            const historyData = await cache.lrange('jupiter_amm_swaps_history', requestedOffset, requestedOffset + requestedLimit - 1);
            const totalCount = await cache.llen('jupiter_amm_swaps_history');

            res.json({
                success: true,
                data: historyData,
                pagination: {
                    limit: requestedLimit,
                    offset: requestedOffset,
                    total: totalCount,
                    returned: historyData.length
                },
                credits_consumed: 0,
                message: 'Jupiter AMM swaps history retrieved successfully'
            });

        } catch (error) {
            console.error('Jupiter AMM swaps history endpoint error:', error);
            res.status(500).json({
                error: 'Failed to retrieve Jupiter AMM swaps history'
            });
        }
    }
);

// Pump.fun AMM Swaps History endpoint - requires enterprise tier
router.get('/pumpfun-amm-swaps/history',
    verifyToken,
    checkEndpointAccess('/api/v1/pumpfun-amm-swaps/history'),
    checkCredits(0),
    consumeCredits(0),
    async (req, res) => {
        try {
            const { limit = 100, offset = 0 } = req.query;
            const maxLimit = 1000; // Higher limit for enterprise users
            const requestedLimit = Math.min(parseInt(limit), maxLimit);
            const requestedOffset = Math.max(parseInt(offset), 0);

            // Get Pump.fun AMM swaps history from Redis
            const historyData = await cache.lrange('pumpfun_amm_swaps_history', requestedOffset, requestedOffset + requestedLimit - 1);
            const totalCount = await cache.llen('pumpfun_amm_swaps_history');

            res.json({
                success: true,
                data: historyData,
                pagination: {
                    limit: requestedLimit,
                    offset: requestedOffset,
                    total: totalCount,
                    returned: historyData.length
                },
                credits_consumed: 0,
                message: 'Pump.fun AMM swaps history retrieved successfully'
            });

        } catch (error) {
            console.error('Pump.fun AMM swaps history endpoint error:', error);
            res.status(500).json({
                error: 'Failed to retrieve Pump.fun AMM swaps history'
            });
        }
    }
);

// Jupiter DCA Orders History endpoint - requires enterprise tier
router.get('/jupiter-dca-orders/history',
    verifyToken,
    checkEndpointAccess('/api/v1/jupiter-dca-orders/history'),
    checkCredits(0),
    consumeCredits(0),
    async (req, res) => {
        try {
            const { limit = 100, offset = 0 } = req.query;
            const maxLimit = 1000; // Higher limit for enterprise users
            const requestedLimit = Math.min(parseInt(limit), maxLimit);
            const requestedOffset = Math.max(parseInt(offset), 0);

            // Get Jupiter DCA orders history from Redis
            const historyData = await cache.lrange('jupiter_dca_orders_history', requestedOffset, requestedOffset + requestedLimit - 1);
            const totalCount = await cache.llen('jupiter_dca_orders_history');

            res.json({
                success: true,
                data: historyData,
                pagination: {
                    limit: requestedLimit,
                    offset: requestedOffset,
                    total: totalCount,
                    returned: historyData.length
                },
                credits_consumed: 0,
                message: 'Jupiter DCA orders history retrieved successfully'
            });

        } catch (error) {
            console.error('Jupiter DCA orders history endpoint error:', error);
            res.status(500).json({
                error: 'Failed to retrieve Jupiter DCA orders history'
            });
        }
    }
);

export default router;
