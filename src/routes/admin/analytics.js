import express from 'express';
import { db } from '../../config/database.js';

const router = express.Router();

// Get credit optimization recommendations
router.get('/credit-optimization', async (req, res) => {
    try {
        const { days = 30 } = req.query;
        
        const recommendations = await db.query(`
            SELECT * FROM get_credit_optimization_recommendations($1)
        `, [parseInt(days)]);
        
        res.json({
            success: true,
            data: recommendations.rows,
            analysis_period_days: parseInt(days),
            generated_at: new Date().toISOString()
        });
    } catch (error) {
        console.error('Credit optimization analysis error:', error);
        res.status(500).json({
            error: 'Failed to generate credit optimization recommendations'
        });
    }
});

// Get daily stream usage statistics
router.get('/stream-usage', async (req, res) => {
    try {
        const { days = 7, stream } = req.query;
        
        let query = `
            SELECT 
                stream_name,
                date,
                total_messages,
                unique_subscribers,
                total_credits_consumed,
                avg_messages_per_user,
                CASE 
                    WHEN unique_subscribers > 0 
                    THEN total_credits_consumed::decimal / unique_subscribers 
                    ELSE 0 
                END as credits_per_user
            FROM stream_usage_analytics 
            WHERE date >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
        `;
        
        const params = [];
        if (stream) {
            query += ' AND stream_name = $1';
            params.push(stream);
        }
        
        query += ' ORDER BY date DESC, stream_name';
        
        const usage = await db.query(query, params);
        
        res.json({
            success: true,
            data: usage.rows,
            period_days: parseInt(days),
            stream_filter: stream || 'all'
        });
    } catch (error) {
        console.error('Stream usage analysis error:', error);
        res.status(500).json({
            error: 'Failed to retrieve stream usage statistics'
        });
    }
});

// Get credit efficiency by tier
router.get('/credit-efficiency', async (req, res) => {
    try {
        const { days = 7 } = req.query;
        
        const efficiency = await db.query(`
            SELECT 
                stream_name,
                tier_name,
                date,
                total_users,
                active_users,
                total_credits_allocated,
                total_credits_used,
                avg_credits_per_user,
                utilization_rate,
                CASE 
                    WHEN total_users > 0 
                    THEN (active_users::decimal / total_users) * 100 
                    ELSE 0 
                END as user_engagement_rate
            FROM credit_efficiency_log 
            WHERE date >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
            ORDER BY date DESC, tier_name, stream_name
        `, []);
        
        res.json({
            success: true,
            data: efficiency.rows,
            period_days: parseInt(days)
        });
    } catch (error) {
        console.error('Credit efficiency analysis error:', error);
        res.status(500).json({
            error: 'Failed to retrieve credit efficiency statistics'
        });
    }
});

// Get tier migration trends
router.get('/tier-migrations', async (req, res) => {
    try {
        const { days = 30 } = req.query;
        
        const migrations = await db.query(`
            SELECT 
                tml.created_at::date as migration_date,
                at_from.name as from_tier,
                at_to.name as to_tier,
                COUNT(*) as migration_count,
                AVG(tml.credits_remaining_before) as avg_credits_before,
                AVG(tml.credits_remaining_after) as avg_credits_after,
                array_agg(DISTINCT tml.migration_reason) as reasons
            FROM tier_migration_log tml
            JOIN access_tiers at_from ON tml.from_tier_id = at_from.id
            JOIN access_tiers at_to ON tml.to_tier_id = at_to.id
            WHERE tml.created_at >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
            GROUP BY tml.created_at::date, at_from.name, at_to.name
            ORDER BY migration_date DESC, migration_count DESC
        `, []);
        
        res.json({
            success: true,
            data: migrations.rows,
            period_days: parseInt(days)
        });
    } catch (error) {
        console.error('Tier migration analysis error:', error);
        res.status(500).json({
            error: 'Failed to retrieve tier migration statistics'
        });
    }
});

// Get comprehensive credit analysis summary
router.get('/credit-summary', async (req, res) => {
    try {
        const { days = 7 } = req.query;
        
        const summary = await db.query(`
            SELECT 
                stream_name,
                COUNT(*) as days_analyzed,
                AVG(total_messages) as avg_daily_messages,
                MAX(total_messages) as peak_daily_messages,
                AVG(unique_subscribers) as avg_daily_users,
                MAX(unique_subscribers) as peak_daily_users,
                AVG(total_credits_consumed) as avg_daily_credits,
                MAX(total_credits_consumed) as peak_daily_credits,
                AVG(basic_tier_days_affordable) as basic_tier_affordability,
                AVG(premium_tier_days_affordable) as premium_tier_affordability,
                CASE 
                    WHEN AVG(basic_tier_days_affordable) < 1 THEN 'CRITICAL'
                    WHEN AVG(basic_tier_days_affordable) < 7 THEN 'WARNING'
                    WHEN AVG(basic_tier_days_affordable) < 30 THEN 'CAUTION'
                    ELSE 'OK'
                END as basic_tier_status,
                CASE 
                    WHEN AVG(premium_tier_days_affordable) < 7 THEN 'WARNING'
                    WHEN AVG(premium_tier_days_affordable) < 30 THEN 'CAUTION'
                    ELSE 'OK'
                END as premium_tier_status
            FROM credit_analysis_summary 
            WHERE date >= CURRENT_DATE - INTERVAL '${parseInt(days)} days'
            GROUP BY stream_name
            ORDER BY avg_daily_messages DESC
        `, []);
        
        res.json({
            success: true,
            data: summary.rows,
            period_days: parseInt(days),
            analysis_date: new Date().toISOString()
        });
    } catch (error) {
        console.error('Credit summary analysis error:', error);
        res.status(500).json({
            error: 'Failed to generate credit analysis summary'
        });
    }
});

// Update daily statistics manually
router.post('/update-stats', async (req, res) => {
    try {
        const { date } = req.body;
        const targetDate = date || new Date().toISOString().split('T')[0];
        
        // Update stream stats
        await db.query('SELECT update_daily_stream_stats($1)', [targetDate]);
        
        // Update credit efficiency stats
        await db.query('SELECT update_credit_efficiency_stats($1)', [targetDate]);
        
        res.json({
            success: true,
            message: `Statistics updated for ${targetDate}`,
            updated_at: new Date().toISOString()
        });
    } catch (error) {
        console.error('Statistics update error:', error);
        res.status(500).json({
            error: 'Failed to update statistics'
        });
    }
});

// Get real-time credit usage for current day
router.get('/realtime-usage', async (req, res) => {
    try {
        const usage = await db.query(`
            SELECT 
                'kol-feed' as stream_name,
                COUNT(*) as messages_today,
                COUNT(DISTINCT user_id) as active_users_today,
                SUM(credits_consumed) as credits_consumed_today,
                AVG(credits_consumed) as avg_credits_per_message
            FROM api_usage_logs 
            WHERE endpoint = 'websocket:/kol-feed' 
            AND DATE(created_at) = CURRENT_DATE
            
            UNION ALL
            
            SELECT 
                'jupiter-amm-swaps' as stream_name,
                COUNT(*) as messages_today,
                COUNT(DISTINCT user_id) as active_users_today,
                SUM(credits_consumed) as credits_consumed_today,
                AVG(credits_consumed) as avg_credits_per_message
            FROM api_usage_logs 
            WHERE endpoint = 'websocket:/jupiter-amm-swaps' 
            AND DATE(created_at) = CURRENT_DATE
            
            UNION ALL
            
            SELECT 
                'pumpfun-amm-swaps' as stream_name,
                COUNT(*) as messages_today,
                COUNT(DISTINCT user_id) as active_users_today,
                SUM(credits_consumed) as credits_consumed_today,
                AVG(credits_consumed) as avg_credits_per_message
            FROM api_usage_logs 
            WHERE endpoint = 'websocket:/pumpfun-amm-swaps' 
            AND DATE(created_at) = CURRENT_DATE
            
            UNION ALL
            
            SELECT 
                'jupiter-dca-orders' as stream_name,
                COUNT(*) as messages_today,
                COUNT(DISTINCT user_id) as active_users_today,
                SUM(credits_consumed) as credits_consumed_today,
                AVG(credits_consumed) as avg_credits_per_message
            FROM api_usage_logs 
            WHERE endpoint = 'websocket:/jupiter-dca-orders' 
            AND DATE(created_at) = CURRENT_DATE
            
            ORDER BY messages_today DESC
        `);
        
        res.json({
            success: true,
            data: usage.rows,
            date: new Date().toISOString().split('T')[0],
            last_updated: new Date().toISOString()
        });
    } catch (error) {
        console.error('Real-time usage error:', error);
        res.status(500).json({
            error: 'Failed to retrieve real-time usage data'
        });
    }
});

export default router;
